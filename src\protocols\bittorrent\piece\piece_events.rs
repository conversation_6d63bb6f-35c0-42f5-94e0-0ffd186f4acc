use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, info, warn};
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// 分片事件类型
#[derive(Debug, Clone)]
pub enum PieceEvent {
    /// 分片下载开始
    DownloadStarted {
        piece_index: u32,
        peer_addr: SocketAddr,
    },
    /// 块下载完成
    BlockDownloaded {
        piece_index: u32,
        block_offset: u32,
        block_size: u32,
        peer_addr: SocketAddr,
        download_time_ms: u64,
    },
    /// 分片下载完成
    PieceDownloaded {
        piece_index: u32,
        piece_size: u64,
        total_download_time_ms: u64,
    },
    /// 分片验证成功
    PieceVerified {
        piece_index: u32,
        verification_time_ms: u64,
    },
    /// 分片验证失败
    PieceVerificationFailed {
        piece_index: u32,
        error: String,
        retry_count: u32,
    },
    /// 分片写入文件成功
    PieceWritten {
        piece_index: u32,
        file_offset: u64,
        write_time_ms: u64,
    },
    /// 分片写入文件失败
    PieceWriteFailed {
        piece_index: u32,
        error: String,
    },
    /// 下载请求超时
    RequestTimeout {
        piece_index: u32,
        block_offset: u32,
        peer_addr: SocketAddr,
        timeout_ms: u64,
    },
    /// 对等点断开连接
    PeerDisconnected {
        peer_addr: SocketAddr,
        active_requests: Vec<(u32, u32)>, // (piece_index, block_offset)
    },
    /// 下载进度更新
    ProgressUpdate {
        downloaded_pieces: u32,
        total_pieces: u32,
        download_speed_bps: u64,
        eta_seconds: Option<u64>,
    },
}

/// 事件监听器特征
pub trait PieceEventListener: Send + Sync {
    /// 处理分片事件
    fn handle_event(&self, event: &PieceEvent);
}

/// 事件统计信息
#[derive(Debug, Clone, Default)]
pub struct EventStats {
    /// 各类事件的计数
    pub event_counts: HashMap<String, u64>,
    /// 总事件数量
    pub total_events: u64,
    /// 平均块下载时间（毫秒）
    pub average_block_download_time_ms: f64,
    /// 平均分片验证时间（毫秒）
    pub average_piece_verification_time_ms: f64,
    /// 平均分片写入时间（毫秒）
    pub average_piece_write_time_ms: f64,
}

impl EventStats {
    /// 更新事件统计
    pub fn update_event(&mut self, event_type: &str) {
        *self.event_counts.entry(event_type.to_string()).or_insert(0) += 1;
        self.total_events += 1;
    }

    /// 更新块下载时间统计
    pub fn update_block_download_time(&mut self, time_ms: u64) {
        let count = self.event_counts.get("BlockDownloaded").copied().unwrap_or(0) as f64;
        if count > 0.0 {
            self.average_block_download_time_ms = 
                (self.average_block_download_time_ms * (count - 1.0) + time_ms as f64) / count;
        } else {
            self.average_block_download_time_ms = time_ms as f64;
        }
    }

    /// 更新分片验证时间统计
    pub fn update_verification_time(&mut self, time_ms: u64) {
        let count = self.event_counts.get("PieceVerified").copied().unwrap_or(0) as f64;
        if count > 0.0 {
            self.average_piece_verification_time_ms = 
                (self.average_piece_verification_time_ms * (count - 1.0) + time_ms as f64) / count;
        } else {
            self.average_piece_verification_time_ms = time_ms as f64;
        }
    }

    /// 更新分片写入时间统计
    pub fn update_write_time(&mut self, time_ms: u64) {
        let count = self.event_counts.get("PieceWritten").copied().unwrap_or(0) as f64;
        if count > 0.0 {
            self.average_piece_write_time_ms = 
                (self.average_piece_write_time_ms * (count - 1.0) + time_ms as f64) / count;
        } else {
            self.average_piece_write_time_ms = time_ms as f64;
        }
    }
}

/// 分片事件管理器
/// 
/// 负责管理分片相关事件的发布和订阅
pub struct PieceEventManager {
    /// 事件发送器
    event_sender: mpsc::UnboundedSender<PieceEvent>,
    /// 事件监听器列表
    listeners: Vec<Arc<dyn PieceEventListener>>,
    /// 事件统计信息
    stats: EventStats,
    /// 是否启用事件统计
    enable_stats: bool,
}

impl PieceEventManager {
    /// 创建新的事件管理器
    pub fn new(enable_stats: bool) -> (Self, mpsc::UnboundedReceiver<PieceEvent>) {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        let manager = Self {
            event_sender: sender,
            listeners: Vec::new(),
            stats: EventStats::default(),
            enable_stats,
        };
        
        (manager, receiver)
    }

    /// 添加事件监听器
    pub fn add_listener(&mut self, listener: Arc<dyn PieceEventListener>) {
        self.listeners.push(listener);
    }

    /// 移除事件监听器
    pub fn remove_listener(&mut self, listener: Arc<dyn PieceEventListener>) {
        self.listeners.retain(|l| !Arc::ptr_eq(l, &listener));
    }

    /// 发布事件
    pub fn publish_event(&mut self, event: PieceEvent) -> Result<(), BitTorrentError> {
        // 更新统计信息
        if self.enable_stats {
            self.update_stats(&event);
        }

        // 通知所有监听器
        for listener in &self.listeners {
            listener.handle_event(&event);
        }

        // 发送到事件通道
        self.event_sender.send(event).map_err(|e| {
            BitTorrentError::from(format!("Failed to send event: {}", e))
        })?;

        Ok(())
    }

    /// 更新统计信息
    fn update_stats(&mut self, event: &PieceEvent) {
        match event {
            PieceEvent::DownloadStarted { .. } => {
                self.stats.update_event("DownloadStarted");
            }
            PieceEvent::BlockDownloaded { download_time_ms, .. } => {
                self.stats.update_event("BlockDownloaded");
                self.stats.update_block_download_time(*download_time_ms);
            }
            PieceEvent::PieceDownloaded { .. } => {
                self.stats.update_event("PieceDownloaded");
            }
            PieceEvent::PieceVerified { verification_time_ms, .. } => {
                self.stats.update_event("PieceVerified");
                self.stats.update_verification_time(*verification_time_ms);
            }
            PieceEvent::PieceVerificationFailed { .. } => {
                self.stats.update_event("PieceVerificationFailed");
            }
            PieceEvent::PieceWritten { write_time_ms, .. } => {
                self.stats.update_event("PieceWritten");
                self.stats.update_write_time(*write_time_ms);
            }
            PieceEvent::PieceWriteFailed { .. } => {
                self.stats.update_event("PieceWriteFailed");
            }
            PieceEvent::RequestTimeout { .. } => {
                self.stats.update_event("RequestTimeout");
            }
            PieceEvent::PeerDisconnected { .. } => {
                self.stats.update_event("PeerDisconnected");
            }
            PieceEvent::ProgressUpdate { .. } => {
                self.stats.update_event("ProgressUpdate");
            }
        }
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> &EventStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = EventStats::default();
    }

    /// 设置是否启用统计
    pub fn set_enable_stats(&mut self, enable: bool) {
        self.enable_stats = enable;
    }

    /// 获取事件发送器的克隆
    pub fn get_sender(&self) -> mpsc::UnboundedSender<PieceEvent> {
        self.event_sender.clone()
    }
}

/// 默认的控制台事件监听器
pub struct ConsoleEventListener {
    /// 是否启用详细日志
    verbose: bool,
}

impl ConsoleEventListener {
    /// 创建新的控制台事件监听器
    pub fn new(verbose: bool) -> Self {
        Self { verbose }
    }
}

impl PieceEventListener for ConsoleEventListener {
    fn handle_event(&self, event: &PieceEvent) {
        match event {
            PieceEvent::DownloadStarted { piece_index, peer_addr } => {
                if self.verbose {
                    debug!("Started downloading piece {} from {}", piece_index, peer_addr);
                }
            }
            PieceEvent::BlockDownloaded { piece_index, block_offset, download_time_ms, .. } => {
                if self.verbose {
                    debug!("Downloaded block {}:{} in {}ms", piece_index, block_offset, download_time_ms);
                }
            }
            PieceEvent::PieceDownloaded { piece_index, total_download_time_ms, .. } => {
                info!("Downloaded piece {} in {}ms", piece_index, total_download_time_ms);
            }
            PieceEvent::PieceVerified { piece_index, verification_time_ms } => {
                info!("Verified piece {} in {}ms", piece_index, verification_time_ms);
            }
            PieceEvent::PieceVerificationFailed { piece_index, error, retry_count } => {
                warn!("Failed to verify piece {} (attempt {}): {}", piece_index, retry_count, error);
            }
            PieceEvent::PieceWritten { piece_index, write_time_ms, .. } => {
                if self.verbose {
                    debug!("Written piece {} to file in {}ms", piece_index, write_time_ms);
                }
            }
            PieceEvent::PieceWriteFailed { piece_index, error } => {
                warn!("Failed to write piece {} to file: {}", piece_index, error);
            }
            PieceEvent::RequestTimeout { piece_index, block_offset, peer_addr, timeout_ms } => {
                warn!("Request timeout for block {}:{} from {} after {}ms", 
                      piece_index, block_offset, peer_addr, timeout_ms);
            }
            PieceEvent::PeerDisconnected { peer_addr, active_requests } => {
                warn!("Peer {} disconnected with {} active requests", peer_addr, active_requests.len());
            }
            PieceEvent::ProgressUpdate { downloaded_pieces, total_pieces, download_speed_bps, eta_seconds } => {
                let progress = (*downloaded_pieces as f64 / *total_pieces as f64) * 100.0;
                let speed_mbps = *download_speed_bps as f64 / 1_048_576.0;
                
                if let Some(eta) = eta_seconds {
                    info!("Progress: {:.1}% ({}/{}) - Speed: {:.2} MB/s - ETA: {}s", 
                          progress, downloaded_pieces, total_pieces, speed_mbps, eta);
                } else {
                    info!("Progress: {:.1}% ({}/{}) - Speed: {:.2} MB/s", 
                          progress, downloaded_pieces, total_pieces, speed_mbps);
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{Arc, Mutex};

    struct TestEventListener {
        events: Arc<Mutex<Vec<PieceEvent>>>,
    }

    impl TestEventListener {
        fn new() -> (Self, Arc<Mutex<Vec<PieceEvent>>>) {
            let events = Arc::new(Mutex::new(Vec::new()));
            let listener = Self {
                events: events.clone(),
            };
            (listener, events)
        }
    }

    impl PieceEventListener for TestEventListener {
        fn handle_event(&self, event: &PieceEvent) {
            self.events.lock().unwrap().push(event.clone());
        }
    }

    #[test]
    fn test_event_manager() {
        let (mut manager, _receiver) = PieceEventManager::new(true);
        let (listener, events) = TestEventListener::new();
        
        manager.add_listener(Arc::new(listener));
        
        // 发布事件
        let event = PieceEvent::PieceVerified {
            piece_index: 1,
            verification_time_ms: 100,
        };
        
        manager.publish_event(event.clone()).unwrap();
        
        // 检查事件是否被监听器接收
        let received_events = events.lock().unwrap();
        assert_eq!(received_events.len(), 1);
        
        // 检查统计信息
        let stats = manager.get_stats();
        assert_eq!(stats.total_events, 1);
        assert_eq!(stats.event_counts.get("PieceVerified"), Some(&1));
    }
}
