use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use tracing::debug;

/// 分片稀有度信息
#[derive(Debug, Clone)]
pub struct PieceRarityInfo {
    /// 分片索引
    pub piece_index: u32,
    /// 拥有此分片的对等点数量
    pub peer_count: usize,
}

/// 对等点可用性管理器
/// 负责跟踪每个对等点拥有的分片以及分片的稀有度
pub struct PeerAvailabilityManager {
    /// 对等点拥有的分片信息 (peer_addr -> piece_indices)
    peer_piece_availability: HashMap<SocketAddr, HashSet<u32>>,
    /// 分片稀有度计数 (piece_index -> count of peers having this piece)
    piece_peer_counts: HashMap<u32, usize>,
}

impl PeerAvailabilityManager {
    /// 创建新的对等点可用性管理器
    pub fn new() -> Self {
        Self {
            peer_piece_availability: HashMap::new(),
            piece_peer_counts: HashMap::new(),
        }
    }

    /// 更新对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `new_peer_pieces` - 对等点的分片集合
    pub fn update_peer_pieces(&mut self, peer_addr: &SocketAddr, new_peer_pieces: &HashSet<u32>) {
        let old_pieces = self.peer_piece_availability.insert(*peer_addr, new_peer_pieces.clone());

        // 更新稀有度计数
        if let Some(old_pieces) = old_pieces {
            // 移除旧的稀有度计数
            for piece_index in &old_pieces {
                if let Some(count) = self.piece_peer_counts.get_mut(piece_index) {
                    *count -= 1;
                    if *count == 0 {
                        self.piece_peer_counts.remove(piece_index);
                    }
                }
            }
        }

        // 添加新的稀有度计数
        for piece_index in new_peer_pieces {
            *self.piece_peer_counts.entry(*piece_index).or_insert(0) += 1;
        }

        debug!("Updated peer pieces for {}: {} pieces", peer_addr, new_peer_pieces.len());
    }
    
    /// 添加对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `piece_index` - 分片索引
    pub fn add_peer_piece(&mut self, peer_addr: &SocketAddr, piece_index: u32) {
        let pieces = self.peer_piece_availability.entry(*peer_addr).or_insert_with(HashSet::new);
        if pieces.insert(piece_index) {
            // 如果是新添加的分片，更新稀有度计数
            *self.piece_peer_counts.entry(piece_index).or_insert(0) += 1;
            debug!("Peer {} now has piece {}. Rarity for piece {} is now {}", 
                   peer_addr, piece_index, piece_index, 
                   self.piece_peer_counts.get(&piece_index).unwrap_or(&0));
        } else {
            debug!("Peer {} already had piece {}", peer_addr, piece_index);
        }
    }

    /// 移除对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `piece_index` - 分片索引
    pub fn remove_peer_piece(&mut self, peer_addr: &SocketAddr, piece_index: u32) {
        if let Some(pieces) = self.peer_piece_availability.get_mut(peer_addr) {
            if pieces.remove(&piece_index) {
                // 如果成功移除分片，更新稀有度计数
                if let Some(count) = self.piece_peer_counts.get_mut(&piece_index) {
                    *count -= 1;
                    if *count == 0 {
                        self.piece_peer_counts.remove(&piece_index);
                    }
                }
                debug!("Removed piece {} from peer {}. Rarity for piece {} is now {}", 
                       piece_index, peer_addr, piece_index, 
                       self.piece_peer_counts.get(&piece_index).unwrap_or(&0));
            }
        }
    }

    /// 移除对等点
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    pub fn remove_peer(&mut self, peer_addr: &SocketAddr) {
        if let Some(pieces) = self.peer_piece_availability.remove(peer_addr) {
            // 更新所有分片的稀有度计数
            for piece_index in pieces {
                if let Some(count) = self.piece_peer_counts.get_mut(&piece_index) {
                    *count -= 1;
                    if *count == 0 {
                        self.piece_peer_counts.remove(&piece_index);
                    }
                }
            }
            debug!("Removed peer {} and updated piece rarities", peer_addr);
        }
    }

    /// 获取对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// 
    /// # 返回值
    /// 返回对等点拥有的分片集合的引用，如果对等点不存在则返回None
    pub fn get_peer_pieces(&self, peer_addr: &SocketAddr) -> Option<&HashSet<u32>> {
        self.peer_piece_availability.get(peer_addr)
    }

    /// 获取分片的稀有度（拥有此分片的对等点数量）
    /// 
    /// # 参数
    /// * `piece_index` - 分片索引
    /// 
    /// # 返回值
    /// 返回拥有此分片的对等点数量
    pub fn get_piece_rarity(&self, piece_index: u32) -> usize {
        self.piece_peer_counts.get(&piece_index).copied().unwrap_or(0)
    }

    /// 获取所有分片的稀有度信息
    /// 
    /// # 返回值
    /// 返回所有分片的稀有度信息向量
    pub fn get_all_piece_rarities(&self) -> Vec<PieceRarityInfo> {
        self.piece_peer_counts
            .iter()
            .map(|(&piece_index, &peer_count)| PieceRarityInfo {
                piece_index,
                peer_count,
            })
            .collect()
    }

    /// 获取分片稀有度数组
    /// 
    /// # 参数
    /// * `total_pieces` - 总分片数量
    /// 
    /// # 返回值
    /// 返回分片稀有度数组，索引对应分片索引，值为拥有该分片的对等点数量
    pub fn get_piece_rarity_array(&self, total_pieces: usize) -> Vec<usize> {
        let mut rarities = vec![0; total_pieces];
        for (&piece_index, &count) in &self.piece_peer_counts {
            if (piece_index as usize) < total_pieces {
                rarities[piece_index as usize] = count;
            }
        }
        rarities
    }

    /// 获取拥有指定分片的对等点列表
    /// 
    /// # 参数
    /// * `piece_index` - 分片索引
    /// 
    /// # 返回值
    /// 返回拥有指定分片的对等点地址列表
    pub fn get_peers_with_piece(&self, piece_index: u32) -> Vec<SocketAddr> {
        self.peer_piece_availability
            .iter()
            .filter_map(|(addr, pieces)| {
                if pieces.contains(&piece_index) {
                    Some(*addr)
                } else {
                    None
                }
            })
            .collect()
    }

    /// 检查对等点是否拥有指定分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `piece_index` - 分片索引
    /// 
    /// # 返回值
    /// 如果对等点拥有指定分片则返回true，否则返回false
    pub fn peer_has_piece(&self, peer_addr: &SocketAddr, piece_index: u32) -> bool {
        self.peer_piece_availability
            .get(peer_addr)
            .map(|pieces| pieces.contains(&piece_index))
            .unwrap_or(false)
    }

    /// 获取对等点数量
    pub fn peer_count(&self) -> usize {
        self.peer_piece_availability.len()
    }

    /// 获取已知分片数量
    pub fn known_piece_count(&self) -> usize {
        self.piece_peer_counts.len()
    }

    /// 清空所有数据
    pub fn clear(&mut self) {
        self.peer_piece_availability.clear();
        self.piece_peer_counts.clear();
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> (usize, usize, usize) {
        let peer_count = self.peer_count();
        let known_piece_count = self.known_piece_count();
        let total_piece_instances = self.piece_peer_counts.values().sum::<usize>();
        (peer_count, known_piece_count, total_piece_instances)
    }
}

impl Default for PeerAvailabilityManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[test]
    fn test_peer_availability_manager() {
        let mut manager = PeerAvailabilityManager::new();
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);

        // 初始状态检查
        assert_eq!(manager.peer_count(), 0);
        assert_eq!(manager.known_piece_count(), 0);

        // 添加对等点分片
        let mut pieces1 = HashSet::new();
        pieces1.insert(0);
        pieces1.insert(1);
        pieces1.insert(2);
        manager.update_peer_pieces(&peer1, &pieces1);

        let mut pieces2 = HashSet::new();
        pieces2.insert(1);
        pieces2.insert(2);
        pieces2.insert(3);
        manager.update_peer_pieces(&peer2, &pieces2);

        // 检查状态
        assert_eq!(manager.peer_count(), 2);
        assert_eq!(manager.known_piece_count(), 4);

        // 检查分片稀有度
        assert_eq!(manager.get_piece_rarity(0), 1); // 只有peer1有
        assert_eq!(manager.get_piece_rarity(1), 2); // peer1和peer2都有
        assert_eq!(manager.get_piece_rarity(2), 2); // peer1和peer2都有
        assert_eq!(manager.get_piece_rarity(3), 1); // 只有peer2有

        // 检查对等点是否拥有分片
        assert!(manager.peer_has_piece(&peer1, 0));
        assert!(!manager.peer_has_piece(&peer1, 3));
        assert!(manager.peer_has_piece(&peer2, 3));
        assert!(!manager.peer_has_piece(&peer2, 0));

        // 移除对等点
        manager.remove_peer(&peer1);
        assert_eq!(manager.peer_count(), 1);
        assert_eq!(manager.get_piece_rarity(0), 0); // peer1被移除后，分片0的稀有度变为0
        assert_eq!(manager.get_piece_rarity(1), 1); // 只剩peer2有分片1
    }

    #[test]
    fn test_piece_rarity_array() {
        let mut manager = PeerAvailabilityManager::new();
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);

        // 添加分片
        manager.add_peer_piece(&peer1, 0);
        manager.add_peer_piece(&peer1, 1);
        manager.add_peer_piece(&peer2, 1);
        manager.add_peer_piece(&peer2, 2);

        // 获取稀有度数组
        let rarities = manager.get_piece_rarity_array(5);
        assert_eq!(rarities, vec![1, 2, 1, 0, 0]);
    }

    #[test]
    fn test_get_peers_with_piece() {
        let mut manager = PeerAvailabilityManager::new();
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);
        let peer3 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8082);

        // 添加分片
        manager.add_peer_piece(&peer1, 1);
        manager.add_peer_piece(&peer2, 1);
        manager.add_peer_piece(&peer3, 2);

        // 获取拥有分片1的对等点
        let peers_with_piece1 = manager.get_peers_with_piece(1);
        assert_eq!(peers_with_piece1.len(), 2);
        assert!(peers_with_piece1.contains(&peer1));
        assert!(peers_with_piece1.contains(&peer2));

        // 获取拥有分片2的对等点
        let peers_with_piece2 = manager.get_peers_with_piece(2);
        assert_eq!(peers_with_piece2.len(), 1);
        assert!(peers_with_piece2.contains(&peer3));

        // 获取拥有不存在分片的对等点
        let peers_with_piece3 = manager.get_peers_with_piece(3);
        assert_eq!(peers_with_piece3.len(), 0);
    }
}
