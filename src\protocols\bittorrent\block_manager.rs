use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::time::Duration;
use tracing::{debug, warn};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::fmt;

use super::block_collector::BlockCollector;
use super::torrent::TorrentInfo;

use async_trait::async_trait;
use crate::protocols::bittorrent::traits::BlockManagerTrait;

/// 分片完成回调接口
#[async_trait]
pub trait PieceCompleteCallback: Send + Sync {
    /// 当一个分片的所有块都收集完成时调用
    async fn on_piece_complete(&mut self, piece_index: u32, piece_data: Vec<u8>) -> Result<bool>;
}

/// 为PieceCompleteCallback实现Debug特性
impl fmt::Debug for dyn PieceCompleteCallback {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "PieceCompleteCallback{{...}}")
    }
}

/// 块添加回调接口
#[async_trait]
pub trait BlockAddedCallback: Send + Sync {
    /// 当一个块被添加到块收集器时调用
    async fn on_block_added(&self, piece_index: u32, progress: f64) -> Result<()>;
}

/// 为BlockAddedCallback实现Debug特性
impl fmt::Debug for dyn BlockAddedCallback {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "BlockAddedCallback{{...}}")
    }
}

/// BlockManager 回调接口（标记性 trait，用于兼容现有代码）
pub trait BlockManagerCallback: PieceCompleteCallback + BlockAddedCallback {}


/// 块管理器，用于管理分片的块
#[derive(Debug)]
pub struct BlockManager {
    /// 块收集器映射（分片索引 -> 块收集器）
    pub block_collectors: HashMap<u32, BlockCollector>,
    /// 默认块大小
    pub block_size: u32,
    /// 超时时间
    pub timeout_duration: Duration,
    /// 分片完成回调
    pub piece_complete_callback: Option<Arc<Mutex<dyn PieceCompleteCallback>>>,
    /// 块添加回调
    pub block_added_callback: Option<Arc<dyn BlockAddedCallback>>,
}

impl Clone for BlockManager {
    /// 实现Clone trait，用于复制BlockManager实例
    fn clone(&self) -> Self {
        Self {
            block_collectors: self.block_collectors.clone(),
            block_size: self.block_size,
            timeout_duration: self.timeout_duration,
            piece_complete_callback: self.piece_complete_callback.clone(),
            block_added_callback: self.block_added_callback.clone(),
        }
    }
}

impl BlockManager {
    /// 创建新的块管理器
    pub fn new(block_size: u32, timeout_seconds: u64) -> Self {
        Self {
            block_collectors: HashMap::new(),
            block_size,
            timeout_duration: Duration::from_secs(timeout_seconds),
            piece_complete_callback: None,
            block_added_callback: None,
        }
    }
    /// 设置分片完成回调
    pub fn set_piece_complete_callback(&mut self, callback: Arc<Mutex<dyn PieceCompleteCallback>>) {
        self.piece_complete_callback = Some(callback);
    }
    /// 设置块添加回调
    pub fn set_block_added_callback(&mut self, callback: Arc<dyn BlockAddedCallback>) {
        self.block_added_callback = Some(callback);
    }
    
    /// 添加块数据
    pub async fn add_block_data(
        &mut self,
        piece_index: u32,
        offset: u32,
        data: Vec<u8>,
        torrent_info: Option<&TorrentInfo>
    ) -> Result<bool> {
        // 如果这是一个完整的分片（而不是块），直接通过回调处理
        if offset == 0 && torrent_info.is_some() {
            let torrent_info = torrent_info.unwrap();
            let piece_size = if piece_index as u64 == (torrent_info.total_size / torrent_info.piece_length) &&
                             torrent_info.total_size % torrent_info.piece_length != 0 {
                // 最后一个分片可能不是完整大小
                torrent_info.total_size % torrent_info.piece_length
            } else {
                torrent_info.piece_length
            };

            if data.len() == piece_size as usize {
                if let Some(callback) = &self.piece_complete_callback {
                    let mut callback_guard = callback.lock().await;
                    return callback_guard.on_piece_complete(piece_index, data).await;
                } else {
                    return Err(anyhow!("No piece complete callback available"));
                }
            }
        }

        // 在获取块收集器之前先克隆回调，避免后续的借用冲突
        let block_added_callback = self.block_added_callback.clone();
        let piece_complete_callback = self.piece_complete_callback.clone();
        
        // 获取或创建块收集器
        let block_collector = self.get_or_create_block_collector(piece_index, torrent_info);

        // 添加块数据
        match block_collector.add_block(offset, data) {
            Ok(true) => {
                debug!("Block added to piece {}, progress: {:.2}%",
                       piece_index, block_collector.progress());
                       
                // 通知回调块已添加
                if let Some(callback) = &block_added_callback {
                    callback.on_block_added(piece_index, block_collector.progress()).await?;
                }

                // 如果所有块都已收集，组装并验证分片
                if block_collector.complete {
                    match block_collector.get_piece_data() {
                        Ok(piece_data) => {
                            // 通过回调添加完整分片数据
                            if let Some(callback) = piece_complete_callback {
                                let mut callback_guard = callback.lock().await;
                                match callback_guard.on_piece_complete(piece_index, piece_data).await {
                                    Ok(true) => {
                                        debug!("Piece {} assembled and added successfully", piece_index);

                                        // 移除块收集器
                                        self.block_collectors.remove(&piece_index);
                                        return Ok(true);
                                    },
                                    Ok(false) => {
                                        debug!("Piece {} already exists or invalid", piece_index);
                                        self.block_collectors.remove(&piece_index);
                                        return Ok(false);
                                    },
                                    Err(e) => {
                                        warn!("Failed to add assembled piece {}: {}", piece_index, e);
                                        // 重置块收集器，以便重新下载
                                        block_collector.reset();
                                        return Err(e);
                                    }
                                }
                            } else {
                                return Err(anyhow!("No piece complete callback available"));
                            }
                        },
                        Err(e) => {
                            warn!("Failed to assemble piece {}: {}", piece_index, e);
                            // 重置块收集器，以便重新下载
                            block_collector.reset();
                            return Err(e);
                        }
                    }
                }

                Ok(true)
            },
            Ok(false) => {
                debug!("Block at offset {} already collected for piece {}", offset, piece_index);
                Ok(false)
            },
            Err(e) => {
                warn!("Failed to add block to piece {}: {}", piece_index, e);
                Err(e)
            }
        }
    }

    /// 获取或创建块收集器
    fn get_or_create_block_collector(&mut self, piece_index: u32, torrent_info: Option<&TorrentInfo>) -> &mut BlockCollector {
        if !self.block_collectors.contains_key(&piece_index) {
            // 计算分片大小
            let piece_size = if let Some(torrent_info) = torrent_info {
                if piece_index as u64 == (torrent_info.total_size / torrent_info.piece_length) &&
                   torrent_info.total_size % torrent_info.piece_length != 0 {
                    // 最后一个分片可能不是完整大小
                    torrent_info.total_size % torrent_info.piece_length
                } else {
                    torrent_info.piece_length
                }
            } else {
                // 如果没有种子信息，使用默认大小
                1024 * 1024 // 1MB
            };

            // 创建新的块收集器
            let collector = BlockCollector::new(piece_index, piece_size, self.block_size);
            self.block_collectors.insert(piece_index, collector);
        }

        self.block_collectors.get_mut(&piece_index).unwrap()
    }

    /// 检查超时的块请求
    pub fn check_timeouts(&mut self) -> Vec<u32> {
        let now = std::time::Instant::now();
        let mut reset_pieces = Vec::new();

        // 收集需要重置的块收集器
        let mut collectors_to_reset = Vec::new();

        // 检查每个块收集器
        for (piece_index, collector) in &mut self.block_collectors {
            // 如果块收集器已完成，跳过
            if collector.complete {
                continue;
            }

            // 检查块收集器是否超时
            if now.duration_since(collector.last_activity) > self.timeout_duration {
                collectors_to_reset.push(*piece_index);
                reset_pieces.push(*piece_index);
            }
        }

        // 重置超时的块收集器
        for piece_index in collectors_to_reset {
            if let Some(collector) = self.block_collectors.get_mut(&piece_index) {
                debug!("Resetting timed out block collector for piece {}", piece_index);
                collector.reset();
            }
        }

        reset_pieces
    }

    /// 获取所有块收集器的进度
    pub fn get_progress(&self) -> HashMap<u32, f64> {
        let mut progress = HashMap::new();
        for (piece_index, collector) in &self.block_collectors {
            progress.insert(*piece_index, collector.progress());
        }
        progress
    }

    /// 添加块收集器
    pub fn add_collector(&mut self, piece_index: u32, collector: BlockCollector) {
        self.block_collectors.insert(piece_index, collector);
    }

    /// 检查是否存在指定分片的块收集器
    pub fn has_collector(&self, piece_index: u32) -> bool {
        self.block_collectors.contains_key(&piece_index)
    }

    /// 清理完成的块收集器
    pub fn cleanup_completed(&mut self) -> Vec<u32> {
        let mut completed = Vec::new();
        let mut to_remove = Vec::new();

        for (piece_index, collector) in &self.block_collectors {
            if collector.complete {
                completed.push(*piece_index);
                to_remove.push(*piece_index);
            }
        }

        for piece_index in to_remove {
            self.block_collectors.remove(&piece_index);
        }

        completed
    }
    
    /// 获取已完成的分片数据
    pub async fn get_completed_piece(&mut self, piece_index: u32) -> Result<Option<Vec<u8>>> {
        // 检查是否有对应的块收集器
        if let Some(collector) = self.block_collectors.get(&piece_index) {
            // 检查块收集器是否已完成
            if collector.complete {
                // 获取分片数据
                match collector.get_piece_data() {
                    Ok(piece_data) => {
                        return Ok(Some(piece_data));
                    },
                    Err(e) => {
                        warn!("Failed to get piece data for piece {}: {}", piece_index, e);
                        return Err(e);
                    }
                }
            }
        }
        
        // 没有找到已完成的分片
        Ok(None)
    }
}

#[async_trait::async_trait]
impl BlockManagerTrait for BlockManager {
    async fn set_piece_complete_callback(&mut self, callback: Arc<Mutex<dyn PieceCompleteCallback>>) {
        self.piece_complete_callback = Some(callback);
    }
    async fn set_block_added_callback(&mut self, callback: Arc<dyn BlockAddedCallback>) {
        self.block_added_callback = Some(callback);
    }
    async fn check_timeouts(&mut self) -> Vec<u32> {
        self.check_timeouts()
    }
}
