//! 对等点过滤器模块
//!
//! 该模块提供了各种对等点过滤器的实现，用于根据不同条件过滤对等点。

use std::net::SocketAddr;
use std::pin::Pin;
use std::future::Future;
use anyhow::Result;

use crate::protocols::bittorrent::peer_quality::models::EnhancedPeerScore;

mod blacklist_filter;
mod performance_filter;
mod geo_filter;
mod composite_filter;

pub use blacklist_filter::*;
pub use performance_filter::*;
pub use geo_filter::*;
pub use composite_filter::*;

/// 对等点过滤器接口
pub trait PeerFilterTrait: Send + Sync {
    /// 过滤对等点
    fn filter(&self, addr: &SocketAddr, score: Option<&EnhancedPeerScore>) -> Pin<Box<dyn Future<Output = Result<bool>> + Send + '_>>;
    
    /// 获取过滤器名称
    fn name(&self) -> &str;
    
    /// 获取过滤器描述
    fn description(&self) -> &str;
}

/// 地理位置服务接口
#[async_trait::async_trait]
pub trait GeoLocationServiceTrait: Send + Sync {
    /// 获取国家/地区代码
    fn get_country_code(&self, addr: &SocketAddr) -> Option<String>;
    
    /// 获取地理位置评分
    fn get_geo_score(&self, addr: &SocketAddr) -> f64;
}