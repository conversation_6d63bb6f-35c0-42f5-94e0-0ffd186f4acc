use crate::protocols::bittorrent::tracker_manager::TrackerResponse;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::core::p2p::peer::Peer;

#[async_trait::async_trait]
pub trait PeerManagerTrait: Send + Sync {
    async fn set_max_peers(&mut self, max: usize) -> Result<(), BitTorrentError>;
    async fn add_peers_from_tracker(&mut self, response: TrackerResponse, torrent_info: &TorrentInfo) -> Result<(), BitTorrentError>;
    async fn get_peers(&self) -> Result<HashMap<SocketAddr, Arc<Mutex<dyn Peer>>>, BitTorrentError>;
    // ...可扩展其它接口...
}
