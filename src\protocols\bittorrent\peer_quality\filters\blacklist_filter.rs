//! 黑名单过滤器实现

use std::net::SocketAddr;
use std::sync::Arc;
use std::pin::Pin;
use std::future::Future;
use anyhow::Result;
use tracing::warn;

use crate::protocols::bittorrent::security::SecurityManager;
use crate::protocols::bittorrent::peer_quality::models::EnhancedPeerScore;
use crate::protocols::bittorrent::peer_quality::filters::PeerFilterTrait;

/// 黑名单过滤器
pub struct BlacklistFilter {
    /// 安全管理器
    security_manager: Arc<tokio::sync::RwLock<SecurityManager>>,
    /// 过滤器名称
    name: String,
    /// 过滤器描述
    description: String,
}

impl BlacklistFilter {
    /// 创建新的黑名单过滤器
    pub fn new(security_manager: Arc<tokio::sync::RwLock<SecurityManager>>) -> Self {
        Self {
            security_manager,
            name: "BlacklistFilter".to_string(),
            description: "过滤黑名单中的对等点".to_string(),
        }
    }
}

impl PeerFilterTrait for BlacklistFilter {
    fn filter(&self, addr: &SocketAddr, _score: Option<&EnhancedPeerScore>) -> Pin<Box<dyn Future<Output = Result<bool>> + Send + '_>> {
        let addr = *addr;
        let security_manager = self.security_manager.clone();
        
        Box::pin(async move {
            // 使用安全管理器检查对等点是否在黑名单中
            // 返回true表示通过过滤，false表示被过滤掉
            let manager = security_manager.read().await;
            match manager.check_blacklist(&addr).await {
                Ok(is_allowed) => Ok(is_allowed),
                Err(e) => {
                    warn!("检查黑名单失败: {}, 默认允许对等点: {}", e, addr);
                    Ok(true) // 如果检查失败，默认允许
                }
            }
        })
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
}