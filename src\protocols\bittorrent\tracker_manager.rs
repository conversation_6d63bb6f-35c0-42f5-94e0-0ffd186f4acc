use anyhow::{Result, anyhow};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, warn};

use crate::config::ConfigManager;
use crate::protocols::bittorrent::traits::tracker_manager_trait::TrackerManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
pub use crate::protocols::bittorrent::tracker::TrackerResponse;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use async_trait::async_trait;

use super::tracker::{TrackerClient};

/// Tracker状态
#[derive(Debug, Clone, PartialEq)]
pub enum TrackerStatus {
    /// 未尝试
    NotTried,
    /// 活跃
    Active,
    /// 失败
    Failed,
    /// 暂时禁用
    Disabled,
}

/// Tracker信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct TrackerInfo {
    /// Tracker URL
    pub url: String,
    /// 状态
    pub status: TrackerStatus,
    /// 上次尝试时间
    pub last_try: Option<Instant>,
    /// 上次成功时间
    pub last_success: Option<Instant>,
    /// 连续失败次数
    pub failure_count: u32,
    /// 禁用到期时间
    pub disabled_until: Option<Instant>,
    /// 更新间隔（秒）
    pub interval: u64,
    /// 最小更新间隔（秒）
    pub min_interval: Option<u64>,
    /// 警告消息
    pub warning_message: Option<String>,
    /// 失败原因
    pub failure_reason: Option<String>,
}

/// Tracker管理器，负责与Tracker交互
#[derive(Clone)]
pub struct TrackerManager {
    /// Tracker客户端
    tracker_client: TrackerClient,
    /// Tracker列表
    trackers: HashMap<String, TrackerInfo>,
    /// 活跃Tracker队列
    active_trackers: VecDeque<String>,
    /// 上次更新时间
    last_update: Instant,
    /// 默认更新间隔（秒）
    default_update_interval: u64,
    /// 是否已初始化
    initialized: bool,
    /// 配置管理器
    config_manager: Option<Arc<ConfigManager>>,
}

impl TrackerInfo {
    /// 创建新的Tracker信息
    pub fn new(url: String) -> Self {
        Self {
            url,
            status: TrackerStatus::NotTried,
            last_try: None,
            last_success: None,
            failure_count: 0,
            disabled_until: None,
            interval: 300, // 默认5分钟
            min_interval: None,
            warning_message: None,
            failure_reason: None,
        }
    }

    /// 检查是否可用
    pub fn is_available(&self) -> bool {
        match self.status {
            TrackerStatus::NotTried => true,
            TrackerStatus::Active => true,
            TrackerStatus::Failed => {
                // 根据失败次数计算退避时间
                let backoff_time = if self.failure_count > 0 {
                    let base_time = 60; // 基础退避时间（秒）
                    let max_backoff = 3600; // 最大退避时间（秒）
                    let backoff = base_time * (2u64.pow(self.failure_count.min(10) - 1));
                    backoff.min(max_backoff)
                } else {
                    0
                };

                if let Some(last_try) = self.last_try {
                    last_try.elapsed().as_secs() >= backoff_time
                } else {
                    true
                }
            },
            TrackerStatus::Disabled => {
                if let Some(disabled_until) = self.disabled_until {
                    Instant::now() >= disabled_until
                } else {
                    false
                }
            },
        }
    }

    /// 检查是否应该更新
    pub fn should_update(&self) -> bool {
        if !self.is_available() {
            return false;
        }

        if let Some(last_success) = self.last_success {
            let min_interval = self.min_interval.unwrap_or_else(|| self.interval);
            last_success.elapsed().as_secs() >= min_interval
        } else {
            true
        }
    }

    /// 标记为成功
    pub fn mark_success(&mut self, response: &TrackerResponse) {
        self.status = TrackerStatus::Active;
        self.last_try = Some(Instant::now());
        self.last_success = Some(Instant::now());
        self.failure_count = 0;
        self.disabled_until = None;
        self.interval = response.interval as u64;
        self.min_interval = response.min_interval.map(|i| i as u64);
        self.warning_message = response.warning_message.clone();
        self.failure_reason = response.failure_reason.clone();
    }

    /// 标记为失败
    pub fn mark_failure(&mut self, reason: Option<String>) {
        self.status = TrackerStatus::Failed;
        self.last_try = Some(Instant::now());
        self.failure_count += 1;
        self.failure_reason = reason;

        // 如果连续失败次数过多，暂时禁用
        if self.failure_count > 5 {
            let disable_time = 60 * (2u64.pow(self.failure_count.min(10) - 1));
            self.disabled_until = Some(Instant::now() + Duration::from_secs(disable_time));
            self.status = TrackerStatus::Disabled;
        }
    }
}

#[async_trait]
impl TrackerManagerTrait for TrackerManager {
    async fn announce(
        &mut self,
        torrent_info: &TorrentInfo,
        event: &str,
        uploaded: u64,
        downloaded: u64,
        left: u64
    ) -> Result<TrackerResponse, BitTorrentError> {
        TrackerManager::announce(self, torrent_info, event, uploaded, downloaded, left)
            .await
            .map_err(BitTorrentError::from)
    }
    async fn init_trackers(&mut self, torrent_info: &TorrentInfo) -> Result<(), BitTorrentError> {
        TrackerManager::init_trackers(self, torrent_info).await.map_err(BitTorrentError::from)
    }
    fn should_update(&self) -> bool {
        TrackerManager::should_update(self)
    }
    fn active_tracker_count(&self) -> usize {
        TrackerManager::active_tracker_count(self)
    }
    fn total_tracker_count(&self) -> usize {
        TrackerManager::total_tracker_count(self)
    }
}

impl TrackerManager {
    /// 创建新的Tracker管理器
    pub fn new(peer_id: String, update_interval: u64) -> Self {
        Self {
            tracker_client: TrackerClient::new(peer_id),
            trackers: HashMap::new(),
            active_trackers: VecDeque::new(),
            last_update: Instant::now(),
            default_update_interval: update_interval,
            initialized: false,
            config_manager: None,
        }
    }

    /// 创建新的Tracker管理器，带配置管理器
    pub fn with_config_manager(peer_id: String, update_interval: u64, config_manager: Arc<ConfigManager>) -> Self {
        Self {
            tracker_client: TrackerClient::new(peer_id),
            trackers: HashMap::new(),
            active_trackers: VecDeque::new(),
            last_update: Instant::now(),
            default_update_interval: update_interval,
            initialized: false,
            config_manager: Some(config_manager),
        }
    }

    /// 设置配置管理器
    pub fn set_config_manager(&mut self, config_manager: Arc<ConfigManager>) {
        self.config_manager = Some(config_manager);
    }

    /// 获取Tracker客户端
    pub fn get_client(&self) -> &TrackerClient {
        &self.tracker_client
    }

    /// 初始化Tracker列表
    pub async fn init_trackers(&mut self, torrent_info: &TorrentInfo) -> Result<()> {
        // 清空现有Tracker
        self.trackers.clear();
        self.active_trackers.clear();

        let mut has_trackers = false;

        // 添加主Tracker
        if let Some(announce) = &torrent_info.announce {
            let tracker_info = TrackerInfo::new(announce.clone());
            self.trackers.insert(announce.clone(), tracker_info);
            self.active_trackers.push_back(announce.clone());
            has_trackers = true;
        }

        // 添加备用Tracker
        if let Some(announce_list) = &torrent_info.announce_list {
            for tier in announce_list {
                for url in tier {
                    if !self.trackers.contains_key(url) {
                        let tracker_info = TrackerInfo::new(url.clone());
                        self.trackers.insert(url.clone(), tracker_info);
                        self.active_trackers.push_back(url.clone());
                        has_trackers = true;
                    }
                }
            }
        }

        // 如果没有找到任何Tracker，使用默认Tracker列表
        if !has_trackers {
            debug!("No trackers found in torrent, using default trackers");

            // 从配置中获取默认Tracker列表
            let default_trackers = if let Some(config_manager) = &self.config_manager {
                config_manager.get_default_trackers().await
            } else {
                // 如果没有配置管理器，使用常量中的默认Tracker
                crate::config::DEFAULT_BT_TRACKERS.iter().map(|s| s.to_string()).collect()
            };

            for url in default_trackers {
                if !self.trackers.contains_key(&url) {
                    let tracker_info = TrackerInfo::new(url.clone());
                    self.trackers.insert(url.clone(), tracker_info);
                    self.active_trackers.push_back(url);
                }
            }

            if !self.trackers.is_empty() {
                debug!("Added {} default trackers", self.trackers.len());
                has_trackers = true;
            }
        }

        if !has_trackers {
            warn!("No trackers available for torrent");
        }

        self.initialized = true;

        Ok(())
    }

    /// 向Tracker宣告
    pub async fn announce(
        &mut self,
        torrent_info: &TorrentInfo,
        event: &str,
        uploaded: u64,
        downloaded: u64,
        left: u64
    ) -> Result<TrackerResponse> {
        if !self.initialized {
            self.init_trackers(torrent_info).await?;
        }

        // 尝试所有可用的Tracker
        let mut last_error = None;
        let mut tried_trackers = 0;

        // 创建活跃Tracker的副本，避免在迭代过程中修改
        let active_trackers: Vec<String> = self.active_trackers.iter().cloned().collect();

        for tracker_url in active_trackers {
            if let Some(tracker_info) = self.trackers.get_mut(&tracker_url) {
                if !tracker_info.is_available() {
                    continue;
                }

                tried_trackers += 1;
                debug!("Announcing to tracker: {}", tracker_url);

                match self.tracker_client.announce_to_url(
                    &tracker_url,
                    torrent_info,
                    event,
                    uploaded,
                    downloaded,
                    left
                ).await {
                    Ok(response) => {
                        // 更新Tracker信息
                        tracker_info.mark_success(&response);

                        // 将成功的Tracker移到队列前面
                        self.active_trackers.retain(|url| url != &tracker_url);
                        self.active_trackers.push_front(tracker_url);

                        // 更新最后更新时间
                        self.last_update = Instant::now();

                        return Ok(response);
                    },
                    Err(e) => {
                        warn!("Failed to announce to tracker {}: {}", tracker_url, e);
                        tracker_info.mark_failure(Some(e.to_string()));
                        last_error = Some(e);
                    }
                }
            }
        }

        // 如果没有尝试任何Tracker，尝试重新初始化
        if tried_trackers == 0 {
            self.init_trackers(torrent_info).await?;
            // 避免递归调用，直接返回错误
            return Err(anyhow!("No available trackers found"));
        }

        // 所有Tracker都失败
        Err(last_error.unwrap_or_else(|| anyhow!("All trackers failed")))
    }

    /// 检查是否应该更新
    pub fn should_update(&self) -> bool {
        self.last_update.elapsed().as_secs() >= self.default_update_interval
    }

    /// 获取上次更新时间
    pub fn last_update(&self) -> Instant {
        self.last_update
    }

    /// 设置更新间隔
    pub fn set_update_interval(&mut self, interval: u64) {
        self.default_update_interval = interval;
    }

    /// 获取更新间隔
    pub fn update_interval(&self) -> u64 {
        self.default_update_interval
    }

    /// 获取所有Tracker信息
    pub fn get_trackers(&self) -> &HashMap<String, TrackerInfo> {
        &self.trackers
    }

    /// 获取所有Tracker信息（可变）
    pub fn get_trackers_mut(&mut self) -> &mut HashMap<String, TrackerInfo> {
        &mut self.trackers
    }

    /// 获取活跃Tracker队列
    pub fn get_active_trackers(&self) -> &VecDeque<String> {
        &self.active_trackers
    }

    /// 获取活跃Tracker队列（可变）
    pub fn get_active_trackers_mut(&mut self) -> &mut VecDeque<String> {
        &mut self.active_trackers
    }

    /// 获取活跃Tracker数量
    pub fn active_tracker_count(&self) -> usize {
        self.trackers.values().filter(|t| t.status == TrackerStatus::Active).count()
    }

    /// 获取总Tracker数量
    pub fn total_tracker_count(&self) -> usize {
        self.trackers.len()
    }

    /// 检查是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }
}
