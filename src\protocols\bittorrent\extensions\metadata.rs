use std::any::Any;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use sha1::{Sha1, Digest};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use super::extension_protocol::ExtensionHandler;
use super::message::{ExtensionMessageTrait, ExtensionMessageType};
use crate::protocols::bittorrent::utils::error::BitTorrentError;

type Result<T> = std::result::Result<T, BitTorrentError>;

/// 元数据消息类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MetadataMessageType {
    /// 请求元数据分片
    Request = 0,
    /// 元数据分片数据
    Data = 1,
    /// 拒绝请求
    Reject = 2,
}

impl From<u8> for MetadataMessageType {
    fn from(value: u8) -> Self {
        match value {
            0 => MetadataMessageType::Request,
            1 => MetadataMessageType::Data,
            2 => MetadataMessageType::Reject,
            _ => MetadataMessageType::Reject,
        }
    }
}

/// 元数据消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetadataMessage {
    /// 消息类型
    #[serde(rename = "msg_type")]
    pub msg_type: u8,

    /// 分片索引
    #[serde(rename = "piece")]
    pub piece: u32,

    /// 总大小 (仅在 Data 消息中)
    #[serde(rename = "total_size")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub total_size: Option<u32>,

    /// 分片数据 (不通过 bencode 序列化，而是附加在消息后)
    #[serde(skip)]
    pub data: Option<Vec<u8>>,
}

impl MetadataMessage {
    /// 创建请求消息
    pub fn request(piece: u32) -> Self {
        Self {
            msg_type: MetadataMessageType::Request as u8,
            piece,
            total_size: None,
            data: None,
        }
    }

    /// 创建数据消息
    pub fn data(piece: u32, total_size: u32, data: Vec<u8>) -> Self {
        Self {
            msg_type: MetadataMessageType::Data as u8,
            piece,
            total_size: Some(total_size),
            data: Some(data),
        }
    }

    /// 创建拒绝消息
    pub fn reject(piece: u32) -> Self {
        Self {
            msg_type: MetadataMessageType::Reject as u8,
            piece,
            total_size: None,
            data: None,
        }
    }
}

impl ExtensionMessageTrait for MetadataMessage {
    fn message_type(&self) -> ExtensionMessageType {
        ExtensionMessageType::Metadata
    }

    fn encode(&self) -> Result<Vec<u8>> {
        let mut encoded = serde_bencode::to_bytes(self)
            .map_err(|e| BitTorrentError::Other(format!("bencode encode error: {}", e)))?;

        // 如果有数据，附加在消息后
        if let Some(data) = &self.data {
            encoded.extend_from_slice(data);
        }

        Ok(encoded)
    }

    fn decode(data: &[u8]) -> Result<Self> {
        // 解码消息头
        let mut message: Self = serde_bencode::from_bytes(data)
            .map_err(|e| BitTorrentError::Other(format!("bencode decode error: {}", e)))?;

        // 如果是数据消息，提取附加的数据
        if message.msg_type == MetadataMessageType::Data as u8 {
            // 找到bencode编码的结束位置
            let mut i = 0;
            let mut depth = 0;

            while i < data.len() {
                match data[i] {
                    b'd' => depth += 1,
                    b'e' => {
                        depth -= 1;
                        if depth == 0 {
                            i += 1;
                            break;
                        }
                    }
                    _ => {}
                }
                i += 1;
            }

            // 提取数据部分
            if i < data.len() {
                message.data = Some(data[i..].to_vec());
            }
        }

        Ok(message)
    }
}

/// 元数据响应回调函数类型
pub type MetadataResponseCallback = Box<dyn Fn(Vec<u8>) -> Result<()> + Send + Sync>;

/// 元数据扩展处理器
pub struct MetadataExtension {
    /// 消息ID
    message_id: u8,

    /// 元数据大小
    metadata_size: Arc<RwLock<Option<u32>>>,

    /// 元数据分片
    metadata_pieces: Arc<RwLock<HashMap<u32, Vec<u8>>>>,

    /// 已请求的分片
    requested_pieces: Arc<RwLock<HashSet<u32>>>,

    /// 元数据哈希
    info_hash: [u8; 20],

    /// 元数据获取完成回调
    on_metadata_complete: Arc<RwLock<Option<Box<dyn Fn(Vec<u8>) -> Result<()> + Send + Sync>>>>,

    /// 分片大小
    piece_size: u32,

    /// 最大并发请求数
    max_requests: u32,

    /// 上次请求时间
    last_request_time: Arc<RwLock<Option<std::time::Instant>>>,

    /// 完整的元数据（用于上传）
    complete_metadata: Arc<RwLock<Option<Vec<u8>>>>,

    /// 是否支持上传
    upload_enabled: Arc<RwLock<bool>>,

    /// 上传限制（每秒最大请求数）
    upload_rate_limit: Arc<RwLock<Option<u32>>>,

    /// 上次上传时间
    last_upload_time: Arc<RwLock<Option<std::time::Instant>>>,

    /// 响应回调函数，用于发送响应消息
    response_callback: Arc<RwLock<Option<MetadataResponseCallback>>>,
}

impl MetadataExtension {
    /// 创建新的元数据扩展处理器
    pub fn new(info_hash: [u8; 20]) -> Self {
        Self {
            message_id: 2, // 默认消息ID
            metadata_size: Arc::new(RwLock::new(None)),
            metadata_pieces: Arc::new(RwLock::new(HashMap::new())),
            requested_pieces: Arc::new(RwLock::new(HashSet::new())),
            info_hash,
            on_metadata_complete: Arc::new(RwLock::new(None)),
            piece_size: 16384, // 16KB
            max_requests: 5,
            last_request_time: Arc::new(RwLock::new(None)),
            complete_metadata: Arc::new(RwLock::new(None)),
            upload_enabled: Arc::new(RwLock::new(false)),
            upload_rate_limit: Arc::new(RwLock::new(None)),
            last_upload_time: Arc::new(RwLock::new(None)),
            response_callback: Arc::new(RwLock::new(None)),
        }
    }

    /// 设置响应回调函数
    pub async fn set_response_callback(&mut self, callback: MetadataResponseCallback) {
        let mut callback_lock = self.response_callback.write().await;
        *callback_lock = Some(callback);
    }

    /// 设置元数据完成回调
    pub async fn set_metadata_complete_callback(&mut self, callback: Box<dyn Fn(Vec<u8>) -> Result<()> + Send + Sync>) {
        let mut callback_lock = self.on_metadata_complete.write().await;
        *callback_lock = Some(callback);
    }

    /// 设置元数据大小
    pub async fn set_metadata_size(&mut self, size: u32) {
        let mut size_lock = self.metadata_size.write().await;
        *size_lock = Some(size);
    }

    /// 获取元数据大小
    pub async fn metadata_size(&self) -> Option<u32> {
        let size_lock = self.metadata_size.read().await;
        *size_lock
    }

    /// 设置完整的元数据（用于上传）
    pub async fn set_complete_metadata(&self, metadata: Vec<u8>) -> Result<()> {
        // 验证元数据哈希
        if !self.verify_metadata(&metadata) {
            return Err(BitTorrentError::Other("Metadata verification failed".to_string()));
        }

        // 设置元数据大小
        let size = metadata.len() as u32;
        let mut size_lock = self.metadata_size.write().await;
        *size_lock = Some(size);

        // 存储完整的元数据
        let mut metadata_lock = self.complete_metadata.write().await;
        *metadata_lock = Some(metadata);

        // 启用上传
        let mut upload_lock = self.upload_enabled.write().await;
        *upload_lock = true;

        info!("Complete metadata set for info hash: {:?}, size: {} bytes", self.info_hash, size);

        Ok(())
    }

    /// 获取完整的元数据
    pub async fn get_complete_metadata(&self) -> Option<Vec<u8>> {
        let metadata_lock = self.complete_metadata.read().await;
        metadata_lock.clone()
    }

    /// 启用或禁用上传
    pub async fn set_upload_enabled(&self, enabled: bool) -> Result<()> {
        // 如果启用上传，确保我们有完整的元数据
        if enabled {
            let metadata_lock = self.complete_metadata.read().await;
            if metadata_lock.is_none() {
                return Err(BitTorrentError::Other("Cannot enable upload without complete metadata".to_string()));
            }
        }

        // 设置上传状态
        let mut upload_lock = self.upload_enabled.write().await;
        *upload_lock = enabled;
        debug!("Metadata upload {} for info hash: {:?}",
               if enabled { "enabled" } else { "disabled" },
               self.info_hash);

        Ok(())
    }

    /// 设置上传速率限制（每秒最大请求数）
    pub async fn set_upload_rate_limit(&self, limit: Option<u32>) -> Result<()> {
        let mut limit_lock = self.upload_rate_limit.write().await;
        *limit_lock = limit;
        debug!("Metadata upload rate limit set to {:?} requests/sec", limit);
        Ok(())
    }

    /// 获取上传速率限制
    pub async fn get_upload_rate_limit(&self) -> Option<u32> {
        let limit_lock = self.upload_rate_limit.read().await;
        *limit_lock
    }

    /// 计算分片数量
    async fn piece_count(&self) -> Option<u32> {
        let size_lock = self.metadata_size.read().await;
        size_lock.map(|size| {
            (size + self.piece_size - 1) / self.piece_size
        })
    }

    /// 检查是否已完成
    async fn is_complete(&self) -> bool {
        if let Some(piece_count) = self.piece_count().await {
            let pieces_lock = self.metadata_pieces.read().await;
            pieces_lock.len() as u32 == piece_count
        } else {
            false
        }
    }

    /// 组装完整的元数据
    async fn assemble_metadata(&self) -> Option<Vec<u8>> {
        if !self.is_complete().await {
            return None;
        }

        let piece_count = self.piece_count().await?;
        let mut metadata = Vec::new();

        let pieces_lock = self.metadata_pieces.read().await;
        for i in 0..piece_count {
            if let Some(piece) = pieces_lock.get(&i) {
                metadata.extend_from_slice(piece);
            } else {
                return None;
            }
        }

        // 截断到正确的大小
        let size_lock = self.metadata_size.read().await;
        if let Some(size) = *size_lock {
            metadata.truncate(size as usize);
        }

        Some(metadata)
    }

    /// 验证元数据
    fn verify_metadata(&self, metadata: &[u8]) -> bool {
        let mut hasher = Sha1::new();
        hasher.update(metadata);
        let hash = hasher.finalize();

        &hash[..] == &self.info_hash[..]
    }

    /// 处理元数据完成
    async fn handle_metadata_complete(&self) -> Result<()> {
        if let Some(metadata) = self.assemble_metadata().await {
            // 验证元数据
            if !self.verify_metadata(&metadata) {
                return Err(BitTorrentError::Other("Metadata verification failed".to_string()));
            }

            // 存储完整的元数据用于上传
            let mut metadata_lock = self.complete_metadata.write().await;
            *metadata_lock = Some(metadata.clone());

            // 启用上传
            let mut upload_lock = self.upload_enabled.write().await;
            *upload_lock = true;

            // 释放锁，避免在调用回调时持有锁
            drop(metadata_lock);
            drop(upload_lock);

            // 调用回调
            let callback_lock = self.on_metadata_complete.read().await;
            if let Some(callback) = &*callback_lock {
                callback(metadata)?;
            }
        }

        Ok(())
    }

    /// 发送拒绝消息
    async fn send_reject_message(&self, piece: u32) -> Result<()> {
        // 获取响应回调
        let response_callback = self.response_callback.read().await;

        // 如果没有响应回调，无法发送响应
        if response_callback.is_none() {
            debug!("No response callback set, cannot send reject message");
            return Ok(());
        }

        // 创建拒绝消息
        let reject_message = MetadataMessage::reject(piece);
        let encoded = reject_message.encode()?;

        // 使用回调发送响应
        if let Err(e) = response_callback.as_ref().unwrap()(encoded) {
            warn!("Failed to send metadata reject response: {}", e);
        }

        Ok(())
    }

    /// 发送数据消息
    async fn send_data_message(&self, piece: u32, total_size: u32, piece_data: Vec<u8>) -> Result<()> {
        // 获取响应回调
        let response_callback = self.response_callback.read().await;

        // 如果没有响应回调，无法发送响应
        if response_callback.is_none() {
            debug!("No response callback set, cannot send data message");
            return Ok(());
        }

        // 创建数据消息
        let data_message = MetadataMessage::data(piece, total_size, piece_data);
        let encoded = data_message.encode()?;

        // 使用回调发送响应
        if let Err(e) = response_callback.as_ref().unwrap()(encoded) {
            warn!("Failed to send metadata data response: {}", e);
        }

        Ok(())
    }

    /// 获取指定索引的元数据分片（用于上传）
    pub async fn get_metadata_piece(&self, piece_index: u32) -> Option<(Vec<u8>, u32)> {
        // 检查是否启用了上传
        let upload_lock = self.upload_enabled.read().await;
        let upload_enabled = *upload_lock;

        if !upload_enabled {
            debug!("Metadata upload is disabled");
            return None;
        }

        // 释放锁，避免在后续操作中持有多个锁
        drop(upload_lock);

        // 获取完整的元数据
        let metadata_lock = self.complete_metadata.read().await;
        let metadata = metadata_lock.clone()?;

        // 释放锁，避免在后续操作中持有多个锁
        drop(metadata_lock);

        // 获取元数据大小
        let metadata_size = metadata.len() as u32;

        // 计算分片数量
        let piece_count = (metadata_size + self.piece_size - 1) / self.piece_size;

        // 检查分片索引是否有效
        if piece_index >= piece_count {
            debug!("Invalid piece index: {}, total pieces: {}", piece_index, piece_count);
            return None;
        }

        // 计算分片起始位置和长度
        let start = piece_index as usize * self.piece_size as usize;
        let end = std::cmp::min(start + self.piece_size as usize, metadata.len());

        // 提取分片数据
        let piece_data = metadata[start..end].to_vec();

        // 应用上传速率限制
        let limit_lock = self.upload_rate_limit.read().await;
        if let Some(limit) = *limit_lock {
            // 检查上次上传时间
            let time_lock = self.last_upload_time.read().await;
            if let Some(last_time) = *time_lock {
                // 计算需要等待的时间
                let elapsed = last_time.elapsed();
                let min_interval = std::time::Duration::from_secs_f64(1.0 / limit as f64);

                if elapsed < min_interval {
                    // 需要等待
                    let wait_time = min_interval - elapsed;
                    debug!("Rate limiting metadata upload, waiting for {:?}", wait_time);

                    // 释放锁，避免在等待时持有锁
                    drop(time_lock);
                    drop(limit_lock);

                    tokio::time::sleep(wait_time).await;
                } else {
                    // 释放锁，避免在后续操作中持有多个锁
                    drop(time_lock);
                    drop(limit_lock);
                }
            } else {
                // 释放锁，避免在后续操作中持有多个锁
                drop(time_lock);
                drop(limit_lock);
            }

            // 更新上次上传时间
            let mut time_lock = self.last_upload_time.write().await;
            *time_lock = Some(std::time::Instant::now());
        } else {
            // 释放锁，避免在后续操作中持有多个锁
            drop(limit_lock);
        }

        Some((piece_data, metadata_size))
    }

    /// 获取下一个要请求的分片
    pub async fn next_piece_to_request(&self) -> Option<u32> {
        if let Some(piece_count) = self.piece_count().await {
            // 获取已请求的分片和已有的分片
            let requested_lock = self.requested_pieces.read().await;
            let requested_pieces = requested_lock.clone();
            drop(requested_lock);

            let pieces_lock = self.metadata_pieces.read().await;
            let metadata_pieces = pieces_lock.keys().cloned().collect::<HashSet<u32>>();
            drop(pieces_lock);

            // 查找下一个要请求的分片
            for i in 0..piece_count {
                if !metadata_pieces.contains(&i) && !requested_pieces.contains(&i) {
                    // 标记为已请求
                    let mut lock = self.requested_pieces.write().await;
                    lock.insert(i);
                    drop(lock);

                    // 更新最后请求时间
                    let mut time_lock = self.last_request_time.write().await;
                    *time_lock = Some(std::time::Instant::now());

                    return Some(i);
                }
            }
        }

        None
    }
}

#[async_trait]
impl ExtensionHandler for MetadataExtension {
    async fn handle_message(&self, _message_id: u8, payload: &[u8]) -> Result<()> {
        // 解码消息
        let message = MetadataMessage::decode(payload)?;

        match MetadataMessageType::from(message.msg_type) {
            MetadataMessageType::Request => {
                // 处理请求消息
                debug!("Received metadata request for piece {}", message.piece);

                // 检查是否启用了上传
                let upload_enabled = *self.upload_enabled.read().await;

                if !upload_enabled {
                    debug!("Metadata upload is disabled, rejecting request for piece {}", message.piece);
                    self.send_reject_message(message.piece).await?;
                    return Ok(());
                }

                // 获取请求的分片
                if let Some((piece_data, total_size)) = self.get_metadata_piece(message.piece).await {
                    debug!("Sending metadata piece {} ({} bytes)", message.piece, piece_data.len());
                    self.send_data_message(message.piece, total_size, piece_data).await?;
                    info!("Uploaded metadata piece {} for info hash: {:?}", message.piece, self.info_hash);
                } else {
                    debug!("Cannot provide metadata piece {}, sending reject", message.piece);
                    self.send_reject_message(message.piece).await?;
                }
            },
            MetadataMessageType::Data => {
                // 处理数据消息
                debug!("Received metadata data for piece {}", message.piece);

                // 更新元数据大小
                if let Some(size) = message.total_size {
                    debug!("Metadata size: {}", size);

                    // 更新元数据大小
                    let mut size_lock = self.metadata_size.write().await;
                    *size_lock = Some(size);
                    drop(size_lock);
                }

                // 保存分片数据
                if let Some(data) = &message.data {
                    debug!("Received piece {} with {} bytes", message.piece, data.len());

                    // 保存分片数据
                    let mut pieces_lock = self.metadata_pieces.write().await;
                    pieces_lock.insert(message.piece, data.clone());

                    // 从已请求列表中移除
                    let mut requested_lock = self.requested_pieces.write().await;
                    requested_lock.remove(&message.piece);
                    drop(requested_lock);

                    // 检查是否已完成
                    let is_complete = pieces_lock.len() as u32 == self.piece_count().await.unwrap_or(0);

                    // 如果已完成，处理元数据
                    if is_complete {
                        // 释放锁，避免死锁
                        drop(pieces_lock);

                        // 处理元数据完成
                        if let Err(e) = self.handle_metadata_complete().await {
                            warn!("Failed to handle metadata complete: {}", e);
                        } else {
                            info!("Metadata download complete for info hash: {:?}", self.info_hash);
                        }
                    }
                }
            },
            MetadataMessageType::Reject => {
                // 处理拒绝消息
                debug!("Received metadata reject for piece {}", message.piece);

                // 从已请求列表中移除，以便稍后重试
                let mut requested_lock = self.requested_pieces.write().await;
                requested_lock.remove(&message.piece);
            },
        }

        Ok(())
    }

    fn extension_id(&self) -> &str {
        "ut_metadata"
    }

    fn message_id(&self) -> u8 {
        self.message_id
    }

    fn set_message_id(&mut self, id: u8) {
        self.message_id = id;
    }

    async fn generate_message(&self) -> Result<Vec<u8>> {
        // 检查是否有下一个要请求的分片
        if let Some(piece) = self.next_piece_to_request().await {
            // 生成请求消息
            debug!("Generating metadata request for piece {}", piece);
            let message = MetadataMessage::request(piece);
            return message.encode();
        }

        // 如果没有要请求的分片，检查是否有未完成的请求
        // 如果有，并且超过了超时时间，则重新请求
        let timeout = std::time::Duration::from_secs(30); // 30秒超时

        let time_lock = self.last_request_time.read().await;
        if let Some(last_time) = *time_lock {
            if last_time.elapsed() > timeout {
                // 释放锁，避免在后续操作中持有多个锁
                drop(time_lock);

                // 清除所有已请求但未收到的分片
                let mut requested_lock = self.requested_pieces.write().await;
                debug!("Clearing timed out requests: {} pieces", requested_lock.len());
                requested_lock.clear();
                drop(requested_lock);

                // 再次尝试获取下一个要请求的分片
                if let Some(piece) = self.next_piece_to_request().await {
                    debug!("Retrying metadata request for piece {}", piece);
                    let message = MetadataMessage::request(piece);
                    return message.encode();
                }
            }
        }

        // 如果没有要请求的分片，生成一个空请求
        // 这通常不会发生，但为了健壮性，我们还是处理这种情况
        debug!("No metadata pieces to request, generating empty request");
        let message = MetadataMessage::request(0);
        message.encode()
    }

    fn as_any(&self) -> &dyn Any {
        self
    }
}
