//! Piece 模块
//! 
//! 该模块包含了 BitTorrent 协议中分片选择和管理的相关功能，
//! 按照单一职责原则将原本集中在 piece_selector.rs 中的代码解耦为多个子模块。

pub mod peer_scoring;
pub mod selection_strategies;
pub mod peer_availability;
pub mod selector;
pub mod factory;

#[cfg(test)]
mod tests;

// 重新导出主要的公共接口
pub use peer_scoring::{PeerScore, PeerScoreManager};
pub use selection_strategies::{
    PieceSelectionStrategy,
    SequentialStrategy,
    RarestFirstStrategy,
    RandomFirstStrategy,
    EndGameStrategy,
    SelectionStrategy
};
pub use peer_availability::{PeerAvailabilityManager, PieceRarityInfo};
pub use selector::{PieceSelector, PieceSelectorConfig, PieceSelectorStats};
pub use factory::{PieceSelectorFactory, NetworkStability, DownloadType};
