use async_trait::async_trait;
use bytes::Bytes;
use std::sync::Arc;
use std::time::{Duration, Instant};

use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::utils::HttpClient;

use super::error::{WebSeedError, WebSeedResult};
use super::performance::WebSeedPerformance;
use super::webseed_source::{WebSeedSourceTrait, WebSeedType};

/// HTTP Seed (BEP 17 Hoffman风格)
#[derive(Clone)]
pub struct HttpSeed {
    /// 基础URL
    url: String,
    /// HTTP客户端
    http_client: HttpClient,
    /// 种子信息
    torrent_info: Arc<TorrentInfo>,
    /// 性能指标
    performance: WebSeedPerformance,
}

impl HttpSeed {
    /// 创建新的HTTP Seed
    pub fn new(url: String, http_client: HttpClient, torrent_info: Arc<TorrentInfo>) -> WebSeedResult<Self> {
        // 确保URL以斜杠结尾
        let url = if url.ends_with('/') {
            url
        } else {
            format!("{}/", url)
        };

        Ok(Self {
            url,
            http_client,
            torrent_info,
            performance: WebSeedPerformance::default(),
        })
    }

    /// 构建分片URL
    fn build_piece_url(&self, piece_index: usize, piece_length: usize) -> String {
        // BEP 17格式: http://webseed.example.com/info_hash/index/piece_length
        format!("{}{}/{}/{}",
            self.url,
            self.torrent_info.info_hash_hex,
            piece_index,
            piece_length
        )
    }
}

#[async_trait]
impl WebSeedSourceTrait for HttpSeed {
    fn url(&self) -> &str {
        &self.url
    }

    fn source_type(&self) -> WebSeedType {
        WebSeedType::HttpSeed
    }

    async fn download_piece(&self, piece_index: usize, piece_length: usize,
                           piece_offset: usize, length: usize) -> WebSeedResult<Bytes> {
        let start_time = Instant::now();

        // 构建分片URL
        let piece_url = self.build_piece_url(piece_index, piece_length);

        // 创建请求
        let mut req = self.http_client.inner().get(&piece_url);

        // 如果请求部分分片，添加范围头
        if piece_offset > 0 || length < piece_length {
            let range_end = piece_offset + length - 1;
            req = req.header(reqwest::header::RANGE, format!("bytes={}-{}", piece_offset, range_end));
        }

        // 发送请求
        let response = req.send().await.map_err(|e| WebSeedError::HttpError(e.to_string()))?;

        // 检查响应状态
        if !response.status().is_success() && response.status() != reqwest::StatusCode::PARTIAL_CONTENT {
            return Err(WebSeedError::HttpError(format!("Failed to download piece: HTTP {}", response.status())));
        }

        // 读取响应体
        let data = response.bytes().await.map_err(|e| WebSeedError::HttpError(e.to_string()))?;

        // 验证数据长度
        if data.len() != length {
            return Err(WebSeedError::DataLengthMismatch {
                expected: length,
                actual: data.len(),
            });
        }

        // 更新性能指标
        let download_time = start_time.elapsed();
        let mut this = self.clone();
        this.update_performance(download_time, data.len(), true);

        Ok(data)
    }

    async fn is_available(&self) -> bool {
        // 检查基础URL是否可访问
        match self.http_client.head(&self.url).await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }

    fn performance(&self) -> &WebSeedPerformance {
        &self.performance
    }

    fn performance_mut(&mut self) -> &mut WebSeedPerformance {
        &mut self.performance
    }

    fn update_performance(&mut self, download_time: Duration,
                         bytes_downloaded: usize, success: bool) {
        self.performance.update(download_time, bytes_downloaded, success);
    }

    fn priority(&self) -> f64 {
        self.performance.calculate_priority()
    }

    fn clone_box(&self) -> Box<dyn WebSeedSourceTrait> {
        Box::new(self.clone())
    }
}
