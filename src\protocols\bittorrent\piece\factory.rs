use super::selector::{PieceSelector, PieceSelectorConfig};
use super::selection_strategies::PieceSelectionStrategy;

/// 分片选择器工厂
/// 
/// 提供多种预配置的分片选择器创建方法，遵循工厂模式。
/// 这样可以轻松创建适合不同场景的选择器实例。
pub struct PieceSelectorFactory;

impl PieceSelectorFactory {
    /// 创建默认的分片选择器
    /// 
    /// 使用稀有优先策略，适合大多数下载场景
    pub fn create_default() -> PieceSelector {
        PieceSelector::with_defaults()
    }

    /// 创建用于快速启动的分片选择器
    /// 
    /// 使用随机首块策略，适合需要快速开始下载的场景
    pub fn create_for_quick_start(total_pieces: usize) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::RandomFirst,
            end_game_threshold: (total_pieces / 20).max(5), // 5% 或至少5个分片
            random_first_pieces: (total_pieces / 50).max(3).min(10), // 2% 或3-10个分片
            total_pieces,
            consider_peer_performance: true,
        };
        PieceSelector::new(config)
    }

    /// 创建用于顺序下载的分片选择器
    /// 
    /// 使用顺序策略，适合流媒体播放等需要顺序数据的场景
    pub fn create_for_sequential_download(total_pieces: usize) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::Sequential,
            end_game_threshold: (total_pieces / 50).max(2), // 2% 或至少2个分片
            random_first_pieces: 0, // 顺序下载不需要随机首块
            total_pieces,
            consider_peer_performance: true,
        };
        PieceSelector::new(config)
    }

    /// 创建用于高性能下载的分片选择器
    /// 
    /// 优化配置以获得最佳下载性能
    pub fn create_for_high_performance(total_pieces: usize) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::RarestFirst,
            end_game_threshold: (total_pieces / 10).max(10), // 10% 或至少10个分片
            random_first_pieces: (total_pieces / 100).max(2).min(5), // 1% 或2-5个分片
            total_pieces,
            consider_peer_performance: true,
        };
        PieceSelector::new(config)
    }

    /// 创建用于低资源环境的分片选择器
    /// 
    /// 减少内存使用和计算开销，适合资源受限的环境
    pub fn create_for_low_resource(total_pieces: usize) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::Sequential,
            end_game_threshold: (total_pieces / 100).max(1), // 1% 或至少1个分片
            random_first_pieces: 1, // 最少的随机首块
            total_pieces,
            consider_peer_performance: false, // 不考虑对等点性能以减少开销
        };
        PieceSelector::new(config)
    }

    /// 创建用于测试的分片选择器
    /// 
    /// 使用较小的阈值，便于测试各种策略切换
    pub fn create_for_testing(total_pieces: usize) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::RandomFirst,
            end_game_threshold: 4, // 固定的小阈值
            random_first_pieces: 4, // 固定的小数量
            total_pieces,
            consider_peer_performance: true,
        };
        PieceSelector::new(config)
    }

    /// 创建自定义配置的分片选择器
    /// 
    /// 允许完全自定义所有参数
    pub fn create_custom(
        initial_strategy: PieceSelectionStrategy,
        end_game_threshold: usize,
        random_first_pieces: usize,
        total_pieces: usize,
        consider_peer_performance: bool,
    ) -> PieceSelector {
        let config = PieceSelectorConfig {
            initial_strategy,
            end_game_threshold,
            random_first_pieces,
            total_pieces,
            consider_peer_performance,
        };
        PieceSelector::new(config)
    }

    /// 根据种子文件大小创建合适的分片选择器
    /// 
    /// 根据文件大小自动选择最合适的配置
    pub fn create_for_file_size(file_size_mb: u64, total_pieces: usize) -> PieceSelector {
        match file_size_mb {
            // 小文件 (< 100MB): 使用顺序下载
            0..=100 => Self::create_for_sequential_download(total_pieces),
            // 中等文件 (100MB - 1GB): 使用快速启动
            101..=1024 => Self::create_for_quick_start(total_pieces),
            // 大文件 (> 1GB): 使用高性能配置
            _ => Self::create_for_high_performance(total_pieces),
        }
    }

    /// 根据网络条件创建合适的分片选择器
    /// 
    /// 根据网络速度和稳定性选择配置
    pub fn create_for_network_condition(
        network_speed_mbps: f64,
        network_stability: NetworkStability,
        total_pieces: usize,
    ) -> PieceSelector {
        match (network_speed_mbps, network_stability) {
            // 慢速不稳定网络
            (speed, NetworkStability::Poor) if speed < 1.0 => {
                Self::create_for_low_resource(total_pieces)
            }
            // 慢速稳定网络
            (speed, NetworkStability::Good) if speed < 5.0 => {
                Self::create_for_sequential_download(total_pieces)
            }
            // 中速网络
            (speed, _) if speed < 50.0 => {
                Self::create_for_quick_start(total_pieces)
            }
            // 高速网络
            _ => Self::create_for_high_performance(total_pieces),
        }
    }

    /// 根据下载类型创建合适的分片选择器
    pub fn create_for_download_type(download_type: DownloadType, total_pieces: usize) -> PieceSelector {
        match download_type {
            DownloadType::Streaming => Self::create_for_sequential_download(total_pieces),
            DownloadType::Background => Self::create_for_low_resource(total_pieces),
            DownloadType::Priority => Self::create_for_high_performance(total_pieces),
            DownloadType::Normal => Self::create_for_quick_start(total_pieces),
        }
    }
}

/// 网络稳定性枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum NetworkStability {
    /// 网络稳定
    Good,
    /// 网络一般
    Fair,
    /// 网络不稳定
    Poor,
}

/// 下载类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DownloadType {
    /// 流媒体下载（需要顺序数据）
    Streaming,
    /// 后台下载（低资源使用）
    Background,
    /// 优先下载（高性能）
    Priority,
    /// 普通下载
    Normal,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_factory_create_default() {
        let selector = PieceSelectorFactory::create_default();
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RarestFirst);
        assert!(selector.is_considering_peer_performance());
    }

    #[test]
    fn test_factory_create_for_quick_start() {
        let total_pieces = 1000;
        let selector = PieceSelectorFactory::create_for_quick_start(total_pieces);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RandomFirst);
        
        let config = selector.get_config();
        assert_eq!(config.total_pieces, total_pieces);
        assert_eq!(config.end_game_threshold, 50); // 1000 / 20 = 50
        assert_eq!(config.random_first_pieces, 10); // min(1000 / 50, 10) = 10
    }

    #[test]
    fn test_factory_create_for_sequential_download() {
        let total_pieces = 500;
        let selector = PieceSelectorFactory::create_for_sequential_download(total_pieces);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::Sequential);
        
        let config = selector.get_config();
        assert_eq!(config.total_pieces, total_pieces);
        assert_eq!(config.random_first_pieces, 0);
    }

    #[test]
    fn test_factory_create_for_low_resource() {
        let total_pieces = 200;
        let selector = PieceSelectorFactory::create_for_low_resource(total_pieces);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::Sequential);
        assert!(!selector.is_considering_peer_performance());
        
        let config = selector.get_config();
        assert_eq!(config.total_pieces, total_pieces);
        assert_eq!(config.random_first_pieces, 1);
    }

    #[test]
    fn test_factory_create_for_file_size() {
        // 小文件
        let selector_small = PieceSelectorFactory::create_for_file_size(50, 100);
        assert_eq!(selector_small.get_strategy(), PieceSelectionStrategy::Sequential);

        // 中等文件
        let selector_medium = PieceSelectorFactory::create_for_file_size(500, 1000);
        assert_eq!(selector_medium.get_strategy(), PieceSelectionStrategy::RandomFirst);

        // 大文件
        let selector_large = PieceSelectorFactory::create_for_file_size(2000, 5000);
        assert_eq!(selector_large.get_strategy(), PieceSelectionStrategy::RarestFirst);
    }

    #[test]
    fn test_factory_create_for_network_condition() {
        // 慢速不稳定网络
        let selector_slow_poor = PieceSelectorFactory::create_for_network_condition(
            0.5, NetworkStability::Poor, 1000
        );
        assert_eq!(selector_slow_poor.get_strategy(), PieceSelectionStrategy::Sequential);
        assert!(!selector_slow_poor.is_considering_peer_performance());

        // 高速网络
        let selector_fast = PieceSelectorFactory::create_for_network_condition(
            100.0, NetworkStability::Good, 1000
        );
        assert_eq!(selector_fast.get_strategy(), PieceSelectionStrategy::RarestFirst);
    }

    #[test]
    fn test_factory_create_for_download_type() {
        // 流媒体下载
        let selector_streaming = PieceSelectorFactory::create_for_download_type(
            DownloadType::Streaming, 1000
        );
        assert_eq!(selector_streaming.get_strategy(), PieceSelectionStrategy::Sequential);

        // 优先下载
        let selector_priority = PieceSelectorFactory::create_for_download_type(
            DownloadType::Priority, 1000
        );
        assert_eq!(selector_priority.get_strategy(), PieceSelectionStrategy::RarestFirst);
    }
}
