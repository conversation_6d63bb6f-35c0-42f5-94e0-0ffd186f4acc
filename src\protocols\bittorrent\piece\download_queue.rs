use std::collections::{HashMap, VecDeque, HashSet};
use std::net::SocketAddr;
use std::time::{Duration, Instant};
use tracing::{debug, warn};
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use super::piece_data::{Piece, Block, BlockState};

/// 下载请求
#[derive(Debug, Clone)]
pub struct DownloadRequest {
    /// 分片索引
    pub piece_index: u32,
    /// 块偏移量
    pub block_offset: u32,
    /// 块大小
    pub block_size: u32,
    /// 请求的对等点
    pub peer_addr: SocketAddr,
    /// 请求时间
    pub request_time: Instant,
    /// 重试次数
    pub retry_count: u32,
}

impl DownloadRequest {
    /// 创建新的下载请求
    pub fn new(piece_index: u32, block_offset: u32, block_size: u32, peer_addr: SocketAddr) -> Self {
        Self {
            piece_index,
            block_offset,
            block_size,
            peer_addr,
            request_time: Instant::now(),
            retry_count: 0,
        }
    }

    /// 检查请求是否超时
    pub fn is_timed_out(&self, timeout: Duration) -> bool {
        Instant::now().duration_since(self.request_time) > timeout
    }

    /// 增加重试次数
    pub fn increment_retry(&mut self) {
        self.retry_count += 1;
        self.request_time = Instant::now();
    }
}

/// 下载队列统计信息
#[derive(Debug, Clone, Default)]
pub struct DownloadQueueStats {
    /// 待下载请求数量
    pub pending_requests: usize,
    /// 活跃请求数量
    pub active_requests: usize,
    /// 已完成请求数量
    pub completed_requests: u64,
    /// 失败请求数量
    pub failed_requests: u64,
    /// 超时请求数量
    pub timed_out_requests: u64,
    /// 平均下载时间（毫秒）
    pub average_download_time_ms: f64,
}

/// 下载队列管理器
/// 
/// 负责管理分片和块的下载队列，包括请求调度、超时处理、重试逻辑等
pub struct DownloadQueueManager {
    /// 待下载的分片队列（按优先级排序）
    pending_pieces: VecDeque<u32>,
    /// 活跃的下载请求 (request_key -> request)
    active_requests: HashMap<String, DownloadRequest>,
    /// 已请求的分片和块 (piece_index -> set of block_offsets)
    requested_blocks: HashMap<u32, HashSet<u32>>,
    /// 请求超时时间
    request_timeout: Duration,
    /// 最大重试次数
    max_retry_count: u32,
    /// 每个对等点的最大并发请求数
    max_requests_per_peer: usize,
    /// 对等点的活跃请求计数 (peer_addr -> count)
    peer_request_counts: HashMap<SocketAddr, usize>,
    /// 统计信息
    stats: DownloadQueueStats,
}

impl DownloadQueueManager {
    /// 创建新的下载队列管理器
    pub fn new(
        request_timeout: Duration,
        max_retry_count: u32,
        max_requests_per_peer: usize,
    ) -> Self {
        Self {
            pending_pieces: VecDeque::new(),
            active_requests: HashMap::new(),
            requested_blocks: HashMap::new(),
            request_timeout,
            max_retry_count,
            max_requests_per_peer,
            peer_request_counts: HashMap::new(),
            stats: DownloadQueueStats::default(),
        }
    }

    /// 创建默认的下载队列管理器
    pub fn default() -> Self {
        Self::new(
            Duration::from_secs(30), // 30秒超时
            3,                       // 最多重试3次
            5,                       // 每个对等点最多5个并发请求
        )
    }

    /// 添加分片到下载队列
    pub fn add_piece(&mut self, piece_index: u32, priority: bool) {
        if priority {
            self.pending_pieces.push_front(piece_index);
        } else {
            self.pending_pieces.push_back(piece_index);
        }
        debug!("Added piece {} to download queue (priority: {})", piece_index, priority);
    }

    /// 批量添加分片到下载队列
    pub fn add_pieces(&mut self, piece_indices: &[u32], priority: bool) {
        for &piece_index in piece_indices {
            self.add_piece(piece_index, priority);
        }
    }

    /// 请求下载块
    pub fn request_block(
        &mut self,
        piece_index: u32,
        block_offset: u32,
        block_size: u32,
        peer_addr: SocketAddr,
    ) -> Result<(), BitTorrentError> {
        // 检查对等点的并发请求数
        let peer_request_count = self.peer_request_counts.get(&peer_addr).copied().unwrap_or(0);
        if peer_request_count >= self.max_requests_per_peer {
            return Err(BitTorrentError::from(format!(
                "Peer {} has reached maximum concurrent requests ({})",
                peer_addr, self.max_requests_per_peer
            )));
        }

        // 检查块是否已经被请求
        let request_key = format!("{}:{}", piece_index, block_offset);
        if self.active_requests.contains_key(&request_key) {
            return Err(BitTorrentError::from(format!(
                "Block {}:{} is already being requested",
                piece_index, block_offset
            )));
        }

        // 创建下载请求
        let request = DownloadRequest::new(piece_index, block_offset, block_size, peer_addr);
        
        // 添加到活跃请求
        self.active_requests.insert(request_key, request);
        
        // 更新请求的块记录
        self.requested_blocks
            .entry(piece_index)
            .or_insert_with(HashSet::new)
            .insert(block_offset);
        
        // 更新对等点请求计数
        *self.peer_request_counts.entry(peer_addr).or_insert(0) += 1;
        
        debug!("Requested block {}:{} from peer {}", piece_index, block_offset, peer_addr);
        Ok(())
    }

    /// 完成块下载
    pub fn complete_block_download(
        &mut self,
        piece_index: u32,
        block_offset: u32,
        download_time: Duration,
    ) -> Result<(), BitTorrentError> {
        let request_key = format!("{}:{}", piece_index, block_offset);
        
        if let Some(request) = self.active_requests.remove(&request_key) {
            // 更新对等点请求计数
            if let Some(count) = self.peer_request_counts.get_mut(&request.peer_addr) {
                *count = count.saturating_sub(1);
                if *count == 0 {
                    self.peer_request_counts.remove(&request.peer_addr);
                }
            }
            
            // 从请求的块记录中移除
            if let Some(blocks) = self.requested_blocks.get_mut(&piece_index) {
                blocks.remove(&block_offset);
                if blocks.is_empty() {
                    self.requested_blocks.remove(&piece_index);
                }
            }
            
            // 更新统计信息
            self.stats.completed_requests += 1;
            let total_time = self.stats.completed_requests as f64;
            let new_time = download_time.as_millis() as f64;
            self.stats.average_download_time_ms = 
                (self.stats.average_download_time_ms * (total_time - 1.0) + new_time) / total_time;
            
            debug!("Completed block download {}:{} in {:?}", piece_index, block_offset, download_time);
            Ok(())
        } else {
            Err(BitTorrentError::from(format!(
                "No active request found for block {}:{}",
                piece_index, block_offset
            )))
        }
    }

    /// 处理超时的请求
    pub fn handle_timeouts(&mut self) -> Vec<DownloadRequest> {
        let mut timed_out_requests = Vec::new();
        let mut keys_to_remove = Vec::new();

        for (key, request) in &self.active_requests {
            if request.is_timed_out(self.request_timeout) {
                timed_out_requests.push(request.clone());
                keys_to_remove.push(key.clone());
            }
        }

        // 移除超时的请求
        for key in keys_to_remove {
            if let Some(request) = self.active_requests.remove(&key) {
                // 更新对等点请求计数
                if let Some(count) = self.peer_request_counts.get_mut(&request.peer_addr) {
                    *count = count.saturating_sub(1);
                    if *count == 0 {
                        self.peer_request_counts.remove(&request.peer_addr);
                    }
                }
                
                // 从请求的块记录中移除
                if let Some(blocks) = self.requested_blocks.get_mut(&request.piece_index) {
                    blocks.remove(&request.block_offset);
                    if blocks.is_empty() {
                        self.requested_blocks.remove(&request.piece_index);
                    }
                }
                
                self.stats.timed_out_requests += 1;
                warn!("Request timed out: {}:{} from peer {}", 
                      request.piece_index, request.block_offset, request.peer_addr);
            }
        }

        timed_out_requests
    }

    /// 重试失败的请求
    pub fn retry_request(&mut self, mut request: DownloadRequest, new_peer: SocketAddr) -> Result<(), BitTorrentError> {
        if request.retry_count >= self.max_retry_count {
            self.stats.failed_requests += 1;
            return Err(BitTorrentError::from(format!(
                "Request {}:{} has exceeded maximum retry count ({})",
                request.piece_index, request.block_offset, self.max_retry_count
            )));
        }

        request.increment_retry();
        request.peer_addr = new_peer;
        
        self.request_block(
            request.piece_index,
            request.block_offset,
            request.block_size,
            new_peer,
        )
    }

    /// 获取下一个要下载的分片
    pub fn get_next_piece(&mut self) -> Option<u32> {
        self.pending_pieces.pop_front()
    }

    /// 检查分片是否有活跃的请求
    pub fn has_active_requests(&self, piece_index: u32) -> bool {
        self.requested_blocks.contains_key(&piece_index)
    }

    /// 获取分片的活跃请求数量
    pub fn get_active_request_count(&self, piece_index: u32) -> usize {
        self.requested_blocks.get(&piece_index).map(|blocks| blocks.len()).unwrap_or(0)
    }

    /// 获取对等点的活跃请求数量
    pub fn get_peer_request_count(&self, peer_addr: &SocketAddr) -> usize {
        self.peer_request_counts.get(peer_addr).copied().unwrap_or(0)
    }

    /// 取消分片的所有请求
    pub fn cancel_piece_requests(&mut self, piece_index: u32) -> usize {
        let mut cancelled_count = 0;
        let mut keys_to_remove = Vec::new();

        // 找到所有相关的请求
        for (key, request) in &self.active_requests {
            if request.piece_index == piece_index {
                keys_to_remove.push(key.clone());
            }
        }

        // 移除请求
        for key in keys_to_remove {
            if let Some(request) = self.active_requests.remove(&key) {
                // 更新对等点请求计数
                if let Some(count) = self.peer_request_counts.get_mut(&request.peer_addr) {
                    *count = count.saturating_sub(1);
                    if *count == 0 {
                        self.peer_request_counts.remove(&request.peer_addr);
                    }
                }
                cancelled_count += 1;
            }
        }

        // 移除请求的块记录
        self.requested_blocks.remove(&piece_index);

        debug!("Cancelled {} requests for piece {}", cancelled_count, piece_index);
        cancelled_count
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> DownloadQueueStats {
        let mut stats = self.stats.clone();
        stats.pending_requests = self.pending_pieces.len();
        stats.active_requests = self.active_requests.len();
        stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = DownloadQueueStats::default();
    }

    /// 清空队列
    pub fn clear(&mut self) {
        self.pending_pieces.clear();
        self.active_requests.clear();
        self.requested_blocks.clear();
        self.peer_request_counts.clear();
    }

    /// 设置请求超时时间
    pub fn set_request_timeout(&mut self, timeout: Duration) {
        self.request_timeout = timeout;
    }

    /// 设置最大重试次数
    pub fn set_max_retry_count(&mut self, max_retry_count: u32) {
        self.max_retry_count = max_retry_count;
    }

    /// 设置每个对等点的最大并发请求数
    pub fn set_max_requests_per_peer(&mut self, max_requests: usize) {
        self.max_requests_per_peer = max_requests;
    }
}

impl Default for DownloadQueueManager {
    fn default() -> Self {
        Self::default()
    }
}
