use anyhow::Result;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Instant, Duration};
use tokio::sync::Mutex;
use async_trait::async_trait;

use crate::core::p2p::dht::{DHT, DHTStatus, DHTEventListener};
use crate::config::Settings;
use super::dht::service::DHTService;
use super::dht::service::DHTServiceConfig;
use super::tracker::TrackerResponse;
use super::tracker::TrackerPeer;
use crate::protocols::bittorrent::traits::dht_manager_trait::DHTManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

#[derive(Clone)]
/// DHT管理器，负责DHT相关功能
pub struct DHTManager {
    /// DHT服务
    dht_service: Arc<Mutex<DHTService>>,
    /// 是否启用DHT
    enabled: bool,
    /// 上次更新时间
    last_update: std::time::Instant,
    /// 更新间隔（秒）
    update_interval: u64,
}

impl DHTManager {
    /// 创建新的DHT管理器
    pub async fn new(settings: &Settings, enabled: bool, update_interval: u64) -> Result<Self> {
        // 如果DHT未启用，创建一个空的服务
        if !enabled {
            // 创建一个空的DHT服务
            let config = DHTServiceConfig {
                client_config: Default::default(),
                auto_start: false,
                maintenance_interval: 0,
                routing_table_path: None,
                routing_table_save_interval: 0,
                load_routing_table: false,
            };
            let empty_service = DHTService::new(config).await?;

            return Ok(Self {
                dht_service: Arc::new(Mutex::new(empty_service)),
                enabled: false,
                last_update: std::time::Instant::now(),
                update_interval,
            });
        }

        // 创建DHT服务
        let dht_service = DHTService::from_settings(settings).await?;
        let dht_service = Arc::new(Mutex::new(dht_service));

        Ok(Self {
            dht_service,
            enabled: true,
            last_update: std::time::Instant::now(),
            update_interval,
        })
    }
    
    /// 添加引导节点到DHT客户端配置
    pub async fn add_bootstrap_node(&self, addr: SocketAddr) -> Result<()> {
        // 如果DHT未启用，直接返回
        if !self.enabled {
            return Err(anyhow::anyhow!("DHT未启用"));
        }
        
        // 获取DHT状态
        let status = self.get_status().await?;
        if !status.initialized {
            return Err(anyhow::anyhow!("DHT未初始化"));
        }
        
        // 获取DHT服务
        let mut service = self.dht_service.lock().await;
        
        // 调用DHT服务的添加引导节点方法
        service.add_bootstrap_node(addr).await
    }

    /// 初始化DHT服务
    pub async fn init(&mut self) -> Result<()> {
        if !self.enabled {
            return Ok(());
        }

        let mut service = self.dht_service.lock().await;
        service.init().await?;

        Ok(())
    }

    /// 添加BitTorrent监听器
    pub async fn add_bittorrent_listener<F>(&self, callback: F) -> Result<()>
    where
        F: Fn(Vec<u8>, Vec<SocketAddr>) -> Result<()> + Send + Sync + 'static,
    {
        if !self.enabled {
            return Ok(());
        }

        let service = self.dht_service.lock().await;

        // 创建一个适配器，将Vec<u8>转换为[u8; 20]
        let adapter = move |info_hash: [u8; 20], peers: Vec<SocketAddr>| {
            // 将[u8; 20]转换为Vec<u8>
            let info_hash_vec = info_hash.to_vec();
            callback(info_hash_vec, peers)
        };

        service.add_bittorrent_listener(adapter).await?;

        Ok(())
    }

    /// 宣布对等点
    pub async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()> {
        if !self.enabled {
            return Ok(());
        }

        let service = self.dht_service.lock().await;
        service.announce_peer(info_hash, port).await?;

        Ok(())
    }

    /// 获取对等点
    pub async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>> {
        if !self.enabled {
            return Ok(Vec::new());
        }

        let service = self.dht_service.lock().await;
        let peers = service.get_peers(info_hash).await?;

        Ok(peers)
    }

    /// 将DHT对等点转换为Tracker响应格式
    pub fn convert_to_tracker_response(&self, peers: Vec<SocketAddr>) -> TrackerResponse {
        let mut tracker_peers = Vec::with_capacity(peers.len());

        for peer_addr in peers {
            tracker_peers.push(TrackerPeer {
                ip: peer_addr.ip(),
                peer_id: None,
                port: peer_addr.port(),
            });
        }

        TrackerResponse {
            interval: 900,
            min_interval: Some(300),
            complete: None,
            incomplete: None,
            peers: tracker_peers,
            warning_message: None,
            failure_reason: None,
        }
    }

    /// 检查是否应该更新
    pub fn should_update(&self) -> bool {
        self.enabled && self.last_update.elapsed().as_secs() >= self.update_interval
    }

    /// 获取上次更新时间
    pub fn last_update(&self) -> std::time::Instant {
        self.last_update
    }

    /// 设置更新间隔
    pub fn set_update_interval(&mut self, interval: u64) {
        self.update_interval = interval;
    }

    /// 获取更新间隔
    pub fn update_interval(&self) -> u64 {
        self.update_interval
    }

    /// 是否启用DHT
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }


}


#[async_trait]
impl DHT for DHTManager {
    async fn init(&mut self) -> Result<()> {
        self.init().await
    }

    async fn start(&mut self) -> Result<()> {
        // 如果DHT未启用，直接返回
        if !self.enabled {
            return Ok(());
        }

        let mut service = self.dht_service.lock().await;
        service.start().await
    }

    async fn stop(&mut self) -> Result<()> {
        // 如果DHT未启用，直接返回
        if !self.enabled {
            return Ok(());
        }

        let mut service = self.dht_service.lock().await;
        service.stop().await
    }

    async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>> {
        self.get_peers(info_hash).await
    }

    async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()> {
        self.announce_peer(info_hash, port).await
    }

    async fn add_event_listener(&mut self, listener: Arc<dyn DHTEventListener>) -> Result<()> {
        // 如果DHT未启用，直接返回
        if !self.enabled {
            return Ok(());
        }

        let mut service = self.dht_service.lock().await;
        service.add_event_listener(listener).await
    }

    async fn remove_event_listener(&mut self, listener_id: usize) -> Result<()> {
        // 如果DHT未启用，直接返回
        if !self.enabled {
            return Ok(());
        }

        let mut service = self.dht_service.lock().await;
        service.remove_event_listener(listener_id).await
    }

    async fn get_status(&self) -> Result<DHTStatus> {
        // 如果DHT未启用，返回未初始化的状态
        if !self.enabled {
            return Ok(DHTStatus {
                initialized: false,
                running: false,
                node_id: None,
                port: 0,
                node_count: 0,
                active_queries: 0,
                discovered_peers: 0,
                uptime: Duration::from_secs(0),
                ipv6_enabled: false,
                ipv6_node_count: 0,
            });
        }

        let service = self.dht_service.lock().await;
        service.get_status().await
    }
}

#[async_trait]
impl DHTManagerTrait for DHTManager {
    async fn init(&mut self) -> Result<(), BitTorrentError> {
        self.init().await.map_err(BitTorrentError::from)
    }
    async fn start(&mut self) -> Result<(), BitTorrentError> {
        if !self.enabled {
            return Ok(());
        }
        let mut service = self.dht_service.lock().await;
        service.start().await.map_err(BitTorrentError::from)
    }
    async fn stop(&mut self) -> Result<(), BitTorrentError> {
        if !self.enabled {
            return Ok(());
        }
        let mut service = self.dht_service.lock().await;
        service.stop().await.map_err(BitTorrentError::from)
    }
    async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>, BitTorrentError> {
        self.get_peers(info_hash).await.map_err(BitTorrentError::from)
    }
    async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<(), BitTorrentError> {
        self.announce_peer(info_hash, port).await.map_err(BitTorrentError::from)
    }
}
