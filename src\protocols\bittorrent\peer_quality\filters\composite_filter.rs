//! 组合过滤器实现

use std::net::SocketAddr;
use std::sync::Arc;
use std::pin::Pin;
use std::future::Future;
use anyhow::Result;
use tracing::warn;

use crate::protocols::bittorrent::peer_quality::models::EnhancedPeerScore;
use crate::protocols::bittorrent::peer_quality::filters::PeerFilterTrait;

/// 组合过滤器
pub struct CompositeFilter {
    /// 过滤器列表
    filters: Vec<Arc<dyn PeerFilterTrait>>,
    /// 过滤器名称
    name: String,
    /// 过滤器描述
    description: String,
}

impl CompositeFilter {
    /// 创建新的组合过滤器
    pub fn new() -> Self {
        Self {
            filters: Vec::new(),
            name: "CompositeFilter".to_string(),
            description: "组合多个过滤器".to_string(),
        }
    }
    
    /// 添加过滤器
    pub fn add_filter(&mut self, filter: Arc<dyn PeerFilterTrait>) {
        self.filters.push(filter);
    }
}

impl PeerFilterTrait for CompositeFilter {
    fn filter(&self, addr: &SocketAddr, score: Option<&EnhancedPeerScore>) -> Pin<Box<dyn Future<Output = Result<bool>> + Send + '_>> {
        let addr = *addr;
        let filters = self.filters.clone();
        let score_clone = score.cloned();
        Box::pin(async move {
            // 所有过滤器都必须通过
            for filter in &filters {
                match filter.filter(&addr, score_clone.as_ref()).await {
                    Ok(passed) => {
                        if !passed {
                            return Ok(false);
                        }
                    },
                    Err(e) => {
                        warn!("过滤器执行失败，默认拒绝: {}", e);
                        return Ok(false);
                    }
                }
            }
            Ok(true)
        })
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
}