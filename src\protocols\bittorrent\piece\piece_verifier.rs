use sha1::{Sha1, Digest};
use std::collections::HashMap;
use tracing::{debug, warn};
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use super::piece_data::Piece;

/// 分片验证结果
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct VerificationResult {
    /// 分片索引
    pub piece_index: u32,
    /// 是否验证成功
    pub is_valid: bool,
    /// 验证后的分片数据（如果验证成功）
    pub data: Option<Vec<u8>>,
    /// 错误信息（如果验证失败）
    pub error: Option<String>,
}

/// 分片验证统计信息
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
pub struct VerificationStats {
    /// 验证成功的分片数量
    pub verified_count: u64,
    /// 验证失败的分片数量
    pub failed_count: u64,
    /// 总验证时间（毫秒）
    pub total_verification_time_ms: u64,
    /// 平均验证时间（毫秒）
    pub average_verification_time_ms: f64,
}

impl VerificationStats {
    /// 更新统计信息
    pub fn update(&mut self, is_valid: bool, verification_time_ms: u64) {
        if is_valid {
            self.verified_count += 1;
        } else {
            self.failed_count += 1;
        }
        
        self.total_verification_time_ms += verification_time_ms;
        let total_count = self.verified_count + self.failed_count;
        self.average_verification_time_ms = self.total_verification_time_ms as f64 / total_count as f64;
    }

    /// 获取验证成功率
    pub fn success_rate(&self) -> f64 {
        let total = self.verified_count + self.failed_count;
        if total == 0 {
            0.0
        } else {
            self.verified_count as f64 / total as f64
        }
    }
}

/// 分片验证器
/// 
/// 负责验证分片的完整性和正确性
pub struct PieceVerifier {
    /// 验证统计信息
    stats: VerificationStats,
    /// 是否启用快速验证模式（跳过某些检查）
    fast_mode: bool,
    /// 验证失败的分片缓存（用于避免重复验证）
    failed_pieces_cache: HashMap<u32, u32>, // piece_index -> failure_count
    /// 最大失败重试次数
    max_retry_count: u32,
}

impl PieceVerifier {
    /// 创建新的分片验证器
    pub fn new(fast_mode: bool, max_retry_count: u32) -> Self {
        Self {
            stats: VerificationStats::default(),
            fast_mode,
            failed_pieces_cache: HashMap::new(),
            max_retry_count,
        }
    }

    /// 创建默认的分片验证器
    pub fn default() -> Self {
        Self::new(false, 3)
    }

    /// 验证分片
    pub fn verify_piece(&mut self, piece: &mut Piece) -> VerificationResult {
        let start_time = std::time::Instant::now();
        let piece_index = piece.index;

        // 检查是否已经达到最大重试次数
        if let Some(&failure_count) = self.failed_pieces_cache.get(&piece_index) {
            if failure_count >= self.max_retry_count {
                warn!("Piece {} has failed verification {} times, skipping", 
                      piece_index, failure_count);
                return VerificationResult {
                    piece_index,
                    is_valid: false,
                    data: None,
                    error: Some(format!("Max retry count ({}) exceeded", self.max_retry_count)),
                };
            }
        }

        // 执行验证
        let result = self.perform_verification(piece);
        
        // 更新统计信息
        let verification_time = start_time.elapsed().as_millis() as u64;
        self.stats.update(result.is_valid, verification_time);

        // 更新失败缓存
        if !result.is_valid {
            let failure_count = self.failed_pieces_cache.entry(piece_index).or_insert(0);
            *failure_count += 1;
            debug!("Piece {} verification failed (attempt {})", piece_index, failure_count);
        } else {
            // 验证成功，清除失败缓存
            self.failed_pieces_cache.remove(&piece_index);
            debug!("Piece {} verification succeeded", piece_index);
        }

        result
    }

    /// 执行实际的验证逻辑
    fn perform_verification(&self, piece: &mut Piece) -> VerificationResult {
        let piece_index = piece.index;

        // 检查是否所有块都已下载
        if !piece.is_complete() {
            return VerificationResult {
                piece_index,
                is_valid: false,
                data: None,
                error: Some("Piece is not complete".to_string()),
            };
        }

        // 在快速模式下，跳过某些检查
        if !self.fast_mode {
            // 检查块的完整性
            if let Err(e) = self.verify_blocks_integrity(piece) {
                return VerificationResult {
                    piece_index,
                    is_valid: false,
                    data: None,
                    error: Some(e.to_string()),
                };
            }
        }

        // 合并所有块数据
        let piece_data = match piece.get_data() {
            Ok(data) => data,
            Err(e) => {
                return VerificationResult {
                    piece_index,
                    is_valid: false,
                    data: None,
                    error: Some(e.to_string()),
                };
            }
        };

        // 验证哈希
        let is_valid = self.verify_hash(&piece_data, &piece.hash);
        
        if is_valid {
            piece.state = PieceState::Verified;
            VerificationResult {
                piece_index,
                is_valid: true,
                data: Some(piece_data),
                error: None,
            }
        } else {
            piece.reset();
            VerificationResult {
                piece_index,
                is_valid: false,
                data: None,
                error: Some("Hash verification failed".to_string()),
            }
        }
    }

    /// 验证块的完整性
    fn verify_blocks_integrity(&self, piece: &Piece) -> Result<(), BitTorrentError> {
        for (i, block) in piece.blocks.iter().enumerate() {
            // 检查块数据是否存在
            if block.data.is_none() {
                return Err(BitTorrentError::from(format!("Block {} has no data", i)));
            }

            // 检查块大小
            let data = block.data.as_ref().unwrap();
            if data.len() != block.size as usize {
                return Err(BitTorrentError::from(format!(
                    "Block {} size mismatch: expected {}, got {}", 
                    i, block.size, data.len()
                )));
            }

            // 检查块偏移量
            let expected_offset = i as u32 * (piece.blocks[0].size);
            if i < piece.blocks.len() - 1 && block.offset != expected_offset {
                return Err(BitTorrentError::from(format!(
                    "Block {} offset mismatch: expected {}, got {}", 
                    i, expected_offset, block.offset
                )));
            }
        }

        Ok(())
    }

    /// 验证哈希
    fn verify_hash(&self, data: &[u8], expected_hash: &[u8]) -> bool {
        let mut hasher = Sha1::new();
        hasher.update(data);
        let actual_hash = hasher.finalize();
        
        actual_hash.as_slice() == expected_hash
    }

    /// 批量验证分片
    pub fn verify_pieces(&mut self, pieces: &mut [Piece]) -> Vec<VerificationResult> {
        let mut results = Vec::with_capacity(pieces.len());
        
        for piece in pieces.iter_mut() {
            if piece.is_complete() && piece.state != PieceState::Verified {
                let result = self.verify_piece(piece);
                results.push(result);
            }
        }
        
        results
    }

    /// 获取验证统计信息
    pub fn get_stats(&self) -> &VerificationStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = VerificationStats::default();
    }

    /// 清除失败缓存
    pub fn clear_failed_cache(&mut self) {
        self.failed_pieces_cache.clear();
    }

    /// 设置快速模式
    pub fn set_fast_mode(&mut self, fast_mode: bool) {
        self.fast_mode = fast_mode;
    }

    /// 设置最大重试次数
    pub fn set_max_retry_count(&mut self, max_retry_count: u32) {
        self.max_retry_count = max_retry_count;
    }

    /// 检查分片是否应该跳过验证
    pub fn should_skip_verification(&self, piece_index: u32) -> bool {
        if let Some(&failure_count) = self.failed_pieces_cache.get(&piece_index) {
            failure_count >= self.max_retry_count
        } else {
            false
        }
    }

    /// 获取分片的失败次数
    pub fn get_failure_count(&self, piece_index: u32) -> u32 {
        self.failed_pieces_cache.get(&piece_index).copied().unwrap_or(0)
    }
}

impl Default for PieceVerifier {
    fn default() -> Self {
        Self::default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_verification_stats() {
        let mut stats = VerificationStats::default();
        
        // 测试初始状态
        assert_eq!(stats.verified_count, 0);
        assert_eq!(stats.failed_count, 0);
        assert_eq!(stats.success_rate(), 0.0);
        
        // 添加一些验证结果
        stats.update(true, 100);
        stats.update(false, 150);
        stats.update(true, 120);
        
        assert_eq!(stats.verified_count, 2);
        assert_eq!(stats.failed_count, 1);
        assert_eq!(stats.success_rate(), 2.0 / 3.0);
        assert_eq!(stats.average_verification_time_ms, (100 + 150 + 120) as f64 / 3.0);
    }

    #[test]
    fn test_piece_verifier_retry_logic() {
        let mut verifier = PieceVerifier::new(false, 2);
        
        // 测试失败重试逻辑
        assert!(!verifier.should_skip_verification(1));
        assert_eq!(verifier.get_failure_count(1), 0);
        
        // 模拟失败
        verifier.failed_pieces_cache.insert(1, 1);
        assert!(!verifier.should_skip_verification(1));
        assert_eq!(verifier.get_failure_count(1), 1);
        
        // 达到最大重试次数
        verifier.failed_pieces_cache.insert(1, 2);
        assert!(verifier.should_skip_verification(1));
        assert_eq!(verifier.get_failure_count(1), 2);
    }
}
