use std::time::Instant;
use sha1::{Sha1, Digest};
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// 块状态
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum BlockState {
    /// 缺失
    Missing,
    /// 已请求
    Requested,
    /// 已下载
    Downloaded,
}

/// BitTorrent分片块
#[derive(Debug, <PERSON><PERSON>)]
pub struct Block {
    /// 分片索引
    pub piece_index: u32,
    /// 块在分片中的偏移量
    pub offset: u32,
    /// 块大小
    pub size: u32,
    /// 块数据
    pub data: Option<Vec<u8>>,
    /// 块状态
    pub state: BlockState,
    /// 请求时间
    pub request_time: Option<Instant>,
}

impl Block {
    /// 创建新的块
    pub fn new(piece_index: u32, offset: u32, size: u32) -> Self {
        Self {
            piece_index,
            offset,
            size,
            data: None,
            state: BlockState::Missing,
            request_time: None,
        }
    }

    /// 设置块数据
    pub fn set_data(&mut self, data: Vec<u8>) -> Result<(), BitTorrentError> {
        if data.len() != self.size as usize {
            return Err(BitTorrentError::from(format!(
                "Block data size mismatch: expected {}, got {}", 
                self.size, 
                data.len()
            )));
        }
        
        self.data = Some(data);
        self.state = BlockState::Downloaded;
        Ok(())
    }

    /// 标记为已请求
    pub fn mark_requested(&mut self) {
        self.state = BlockState::Requested;
        self.request_time = Some(Instant::now());
    }

    /// 重置块状态
    pub fn reset(&mut self) {
        self.data = None;
        self.state = BlockState::Missing;
        self.request_time = None;
    }

    /// 检查是否超时
    pub fn is_timed_out(&self, timeout: std::time::Duration) -> bool {
        if self.state != BlockState::Requested {
            return false;
        }
        
        if let Some(request_time) = self.request_time {
            Instant::now().duration_since(request_time) > timeout
        } else {
            false
        }
    }
}

/// BitTorrent分片
#[derive(Debug, Clone)]
pub struct Piece {
    /// 分片索引
    pub index: u32,
    /// 分片大小
    pub size: u64,
    /// 分片哈希
    pub hash: Vec<u8>,
    /// 分片状态
    pub state: PieceState,
    /// 分片优先级
    pub priority: u8,
    /// 分片块
    pub blocks: Vec<Block>,
    /// 已下载的块数量
    pub blocks_downloaded: usize,
    /// 总块数
    pub blocks_total: usize,
    /// 下载进度
    pub progress: f64,
    /// 稀有度（拥有此分片的对等点数量）
    pub rarity: usize,
}

impl Piece {
    /// 创建新的分片
    pub fn new(index: u32, size: u64, hash: Vec<u8>, block_size: u32) -> Self {
        // 计算块数量
        let blocks_total = ((size + block_size as u64 - 1) / block_size as u64) as usize;
        let mut blocks = Vec::with_capacity(blocks_total);

        // 创建块
        for i in 0..blocks_total {
            let offset = i as u32 * block_size;
            let actual_block_size = if i == blocks_total - 1 && size % block_size as u64 != 0 {
                (size % block_size as u64) as u32
            } else {
                block_size
            };

            blocks.push(Block::new(index, offset, actual_block_size));
        }

        Self {
            index,
            size,
            hash,
            state: PieceState::Missing,
            priority: 1, // 默认优先级
            blocks,
            blocks_downloaded: 0,
            blocks_total,
            progress: 0.0,
            rarity: 0,
        }
    }

    /// 添加块数据
    pub fn add_block_data(&mut self, offset: u32, data: Vec<u8>) -> Result<bool, BitTorrentError> {
        // 查找对应的块
        for block in &mut self.blocks {
            if block.offset == offset {
                block.set_data(data)?;
                self.blocks_downloaded += 1;

                // 更新进度
                self.progress = self.blocks_downloaded as f64 / self.blocks_total as f64 * 100.0;

                // 如果所有块都已下载，更新分片状态
                if self.blocks_downloaded == self.blocks_total {
                    self.state = PieceState::Downloaded;
                }

                return Ok(true);
            }
        }

        Err(BitTorrentError::from(format!("Block not found for offset {}", offset)))
    }

    /// 验证分片
    pub fn verify(&mut self) -> Result<bool, BitTorrentError> {
        // 检查是否所有块都已下载
        if self.blocks_downloaded < self.blocks_total {
            return Err(BitTorrentError::from("Cannot verify incomplete piece"));
        }

        // 合并所有块数据
        let mut piece_data = Vec::with_capacity(self.size as usize);
        for block in &self.blocks {
            if let Some(data) = &block.data {
                piece_data.extend_from_slice(data);
            } else {
                return Err(BitTorrentError::from("Missing block data"));
            }
        }

        // 计算哈希
        let mut hasher = Sha1::new();
        hasher.update(&piece_data);
        let hash = hasher.finalize().to_vec();

        // 验证哈希
        let valid = hash == self.hash;
        if valid {
            self.state = PieceState::Verified;
        } else {
            // 重置分片状态
            self.reset();
        }

        Ok(valid)
    }

    /// 获取下一个要请求的块
    pub fn next_block(&mut self) -> Option<&mut Block> {
        for block in &mut self.blocks {
            if block.state == BlockState::Missing {
                block.mark_requested();
                return Some(block);
            }
        }
        None
    }

    /// 重置超时的块请求
    pub fn reset_timed_out_blocks(&mut self, timeout: std::time::Duration) -> usize {
        let mut reset_count = 0;

        for block in &mut self.blocks {
            if block.is_timed_out(timeout) {
                block.reset();
                reset_count += 1;
            }
        }

        reset_count
    }

    /// 设置分片优先级
    pub fn set_priority(&mut self, priority: u8) {
        self.priority = priority;
    }

    /// 重置分片状态
    pub fn reset(&mut self) {
        self.state = PieceState::Failed;
        self.blocks_downloaded = 0;
        self.progress = 0.0;
        for block in &mut self.blocks {
            block.reset();
        }
    }

    /// 获取分片数据
    pub fn get_data(&self) -> Result<Vec<u8>, BitTorrentError> {
        if self.state != PieceState::Verified {
            return Err(BitTorrentError::from("Piece is not verified"));
        }

        let mut piece_data = Vec::with_capacity(self.size as usize);
        for block in &self.blocks {
            if let Some(data) = &block.data {
                piece_data.extend_from_slice(data);
            } else {
                return Err(BitTorrentError::from("Missing block data"));
            }
        }

        Ok(piece_data)
    }

    /// 检查分片是否完整
    pub fn is_complete(&self) -> bool {
        self.blocks_downloaded == self.blocks_total
    }

    /// 检查分片是否已验证
    pub fn is_verified(&self) -> bool {
        self.state == PieceState::Verified
    }

    /// 获取缺失的块数量
    pub fn missing_blocks_count(&self) -> usize {
        self.blocks_total - self.blocks_downloaded
    }

    /// 获取已请求但未下载的块数量
    pub fn requested_blocks_count(&self) -> usize {
        self.blocks.iter()
            .filter(|block| block.state == BlockState::Requested)
            .count()
    }
}
