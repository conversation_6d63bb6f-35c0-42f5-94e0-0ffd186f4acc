//! 性能过滤器实现

use std::net::SocketAddr;
use std::pin::Pin;
use std::future::Future;
use anyhow::Result;

use crate::protocols::bittorrent::peer_quality::models::EnhancedPeerScore;
use crate::protocols::bittorrent::peer_quality::filters::PeerFilterTrait;

/// 性能过滤器
pub struct PerformanceFilter {
    /// 最低评分阈值
    threshold: f64,
    /// 过滤器名称
    name: String,
    /// 过滤器描述
    description: String,
}

impl PerformanceFilter {
    /// 创建新的性能过滤器
    pub fn new(threshold: f64) -> Self {
        Self {
            threshold,
            name: "PerformanceFilter".to_string(),
            description: format!("过滤评分低于{}的对等点", threshold),
        }
    }
}

impl PeerFilterTrait for PerformanceFilter {
    fn filter(&self, _addr: &SocketAddr, score: Option<&EnhancedPeerScore>) -> Pin<Box<dyn Future<Output = Result<bool>> + Send + '_>> {
        let threshold = self.threshold;
        let score_value = score.map(|s| s.overall_score);
        Box::pin(async move {
            // 如果没有评分信息，默认通过
            if let Some(score) = score_value {
                Ok(score >= threshold)
            } else {
                Ok(true)
            }
        })
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
}