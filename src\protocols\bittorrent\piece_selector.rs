//! 兼容性包装器 - piece_selector.rs
//!
//! 这个文件现在作为向后兼容性包装器存在，将调用委托给新的 piece 模块。
//! 建议新代码直接使用 `super::piece` 模块中的类型和功能。

use std::collections::HashSet;
use std::net::SocketAddr;
use std::time::Duration;

use crate::core::p2p::piece::PieceState;
use super::piece::{
    PieceSelectionStrategy,
    PieceSelector as NewPieceSelector,
    PieceSelectorConfig,
    PeerScore as NewPeerScore,
    PieceSelectorFactory
};

// 重新导出类型以保持向后兼容性
pub use super::piece::{PeerScore, PieceSelectionStrategy};

/// 兼容性包装器 - 分片选择器
///
/// 这个结构体现在是新 piece 模块的包装器，保持向后兼容性。
/// 建议新代码直接使用 `super::piece::PieceSelector`。
pub struct PieceSelector {
    /// 内部的新实现
    inner: NewPieceSelector,
}

impl PieceSelector {
    /// 创建新的分片选择器（兼容性方法）
    pub fn new(
        initial_strategy: PieceSelectionStrategy,
        end_game_threshold: usize,
        random_first_pieces: usize,
        total_pieces: usize
    ) -> Self {
        let config = PieceSelectorConfig {
            initial_strategy,
            end_game_threshold,
            random_first_pieces,
            total_pieces,
            consider_peer_performance: true,
        };
        Self {
            inner: NewPieceSelector::new(config),
        }
    }
}

/// 为PieceSelector实现Default trait
impl Default for PieceSelector {
    /// 创建默认的分片选择器
    fn default() -> Self {
        Self {
            inner: NewPieceSelector::with_defaults(),
        }
    }
}

impl PieceSelector {
    /// 更新已验证的分片数量
    pub fn update_verified_pieces(&mut self, verified_pieces: usize) {
        self.inner.update_verified_pieces(verified_pieces);
    }

    /// 获取当前策略
    pub fn get_strategy(&self) -> PieceSelectionStrategy {
        self.inner.get_strategy()
    }

    /// 设置策略
    pub fn set_strategy(&mut self, strategy: PieceSelectionStrategy) {
        self.inner.set_strategy(strategy);
    }

    /// 设置总分片数量
    pub fn set_total_pieces(&mut self, total_pieces: u32) {
        self.inner.set_total_pieces(total_pieces);
    }

    /// 设置是否考虑对等点性能
    pub fn set_consider_peer_performance(&mut self, consider: bool) {
        self.inner.set_consider_peer_performance(consider);
    }

    /// 更新对等点评分
    ///
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `download_speed` - 对等点的下载速度（字节/秒）
    /// * `success` - 是否成功完成请求
    /// * `response_time` - 响应时间（毫秒）
    pub fn update_peer_score(&mut self, peer_addr: SocketAddr, download_speed: u64, success: bool, response_time: u64) {
        self.inner.update_peer_score(peer_addr, download_speed, success, response_time);
    }

    /// 获取对等点评分
    pub fn get_peer_score(&self, peer_addr: &SocketAddr) -> Option<PeerScore> {
        self.inner.get_peer_score(peer_addr)
    }

    /// 获取所有对等点评分（兼容性方法）
    pub fn get_all_peer_scores(&self) -> std::collections::HashMap<SocketAddr, PeerScore> {
        // 注意：这个方法返回一个拷贝，因为新实现不直接暴露内部HashMap
        let mut scores = std::collections::HashMap::new();
        let stats = self.inner.get_stats();
        // 这里我们无法直接获取所有评分，所以返回空的HashMap
        // 建议使用新的API方法
        scores
    }

    /// 选择下一个要下载的分片
    pub fn select_next_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        self.inner.select_next_piece(peer_pieces, piece_states, requested_pieces)
    }

    /// 为特定对等点选择下一个要下载的分片
    pub fn select_next_piece_for_peer(
        &self,
        peer_addr: &SocketAddr,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        self.inner.select_next_piece_for_peer(peer_addr, peer_pieces, piece_states, requested_pieces)
    }

    /// 更新对等点拥有的分片
    ///
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `peer_pieces` - 对等点的分片
    pub fn update_peer_pieces(&mut self, peer_addr: &SocketAddr, new_peer_pieces: &HashSet<u32>) {
        self.inner.update_peer_pieces(peer_addr, new_peer_pieces);
    }

    /// 添加对等点拥有的分片
    ///
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `piece_index` - 分片索引
    pub fn add_peer_piece(&mut self, peer_addr: &SocketAddr, piece_index: u32) {
        self.inner.add_peer_piece(peer_addr, piece_index);
    }

    /// 清理过期的对等点评分
    pub fn cleanup_peer_scores(&mut self, max_age: Duration) {
        self.inner.cleanup_expired_peer_scores(max_age);
    }

}

// 注意：测试代码已迁移到 piece 模块中
// 这个文件现在只是一个兼容性包装器
// 新的测试请参考 super::piece::tests
