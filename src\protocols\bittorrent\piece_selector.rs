use std::collections::{HashSet, HashMap};
use std::net::SocketAddr;
use std::time::Instant;
use tracing::{info, debug};

use crate::core::p2p::piece::PieceState;
use super::piece::PieceSelectionStrategy;

/// 对等点性能评分
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub struct PeerScore {
    /// 下载速度 (bytes/s)
    pub download_speed: u64,
    /// 成功率 (0.0-1.0)
    pub success_rate: f64,
    /// 响应时间 (ms)
    pub response_time: u64,
    /// 综合评分 (0.0-1.0)
    pub overall_score: f64,
    /// 最后更新时间
    pub last_update: Instant,
}

impl PeerScore {
    /// 创建新的对等点评分
    pub fn new() -> Self {
        Self {
            download_speed: 0,
            success_rate: 1.0,
            response_time: 0,
            overall_score: 0.5, // 默认中等评分
            last_update: Instant::now(),
        }
    }

    /// 更新评分
    pub fn update(&mut self, download_speed: u64, success: bool, response_time: u64) {
        // 更新下载速度（使用指数移动平均）
        if self.download_speed == 0 {
            self.download_speed = download_speed;
        } else {
            self.download_speed = (self.download_speed * 7 + download_speed * 3) / 10;
        }

        // 更新成功率（使用指数移动平均）
        let success_value = if success { 1.0 } else { 0.0 };
        self.success_rate = self.success_rate * 0.7 + success_value * 0.3;

        // 更新响应时间（使用指数移动平均）
        if self.response_time == 0 {
            self.response_time = response_time;
        } else {
            self.response_time = (self.response_time * 7 + response_time * 3) / 10;
        }

        // 计算综合评分
        self.calculate_overall_score();

        // 更新最后更新时间
        self.last_update = Instant::now();
    }

    /// 计算综合评分
    pub fn calculate_overall_score(&mut self) {
        // 速度评分 (0.0-1.0)，假设1MB/s是满分
        let speed_score = (self.download_speed as f64 / 1_048_576.0).min(1.0);

        // 响应时间评分 (0.0-1.0)，假设50ms以下是满分，1000ms以上是0分
        let response_score = if self.response_time < 50 {
            1.0
        } else if self.response_time > 1000 {
            0.0
        } else {
            1.0 - (self.response_time - 50) as f64 / 950.0
        };

        // 综合评分，权重: 速度60%, 成功率30%, 响应时间10%
        self.overall_score = speed_score * 0.6 + self.success_rate * 0.3 + response_score * 0.1;
    }
}

/// 分片选择器，负责选择下一个要下载的分片
pub struct PieceSelector {
    /// 当前选择策略
    strategy: PieceSelectionStrategy,
    /// 端游模式阈值（剩余分片数量）
    end_game_threshold: usize,
    /// 随机首块数量
    random_first_pieces: usize,
    /// 已验证的分片数量
    verified_pieces: usize,
    /// 总分片数量
    total_pieces: usize,
    /// 对等点评分
    peer_scores: HashMap<SocketAddr, PeerScore>,
    /// 是否考虑对等点性能
    consider_peer_performance: bool,
    /// 对等点拥有的分片信息
    peer_piece_availability: HashMap<SocketAddr, HashSet<u32>>,
    /// 分片稀有度计数 (piece_index -> count of peers having this piece)
    piece_peer_counts: HashMap<u32, usize>,
}

impl PieceSelector {
    /// 创建新的分片选择器
    pub fn new(
        initial_strategy: PieceSelectionStrategy,
        end_game_threshold: usize,
        random_first_pieces: usize,
        total_pieces: usize
    ) -> Self {
        Self {
            strategy: initial_strategy,
            end_game_threshold,
            random_first_pieces,
            verified_pieces: 0,
            total_pieces,
            peer_scores: HashMap::new(),
            consider_peer_performance: true,
            peer_piece_availability: HashMap::new(),
            piece_peer_counts: HashMap::new(),
        }
    }
}

/// 为PieceSelector实现Default trait
impl Default for PieceSelector {
    /// 创建默认的分片选择器
    /// 默认使用稀有优先策略，端游阈值为总分片数的5%，随机首块为5个，总分片数为1000（临时值，会在set_piece_selector中更新）
    fn default() -> Self {
        Self {
            strategy: PieceSelectionStrategy::RarestFirst,
            end_game_threshold: 50, // 默认为50个分片
            random_first_pieces: 5,
            verified_pieces: 0,
            total_pieces: 1000, // 临时值，会在实际使用时更新
            peer_scores: HashMap::new(),
            consider_peer_performance: true,
            peer_piece_availability: HashMap::new(),
            piece_peer_counts: HashMap::new(),
        }
    }
}

impl PieceSelector {

    /// 更新已验证的分片数量
    pub fn update_verified_pieces(&mut self, verified_pieces: usize) {
        self.verified_pieces = verified_pieces;

        // 检查是否应该切换策略
        self.check_strategy_switch();
    }

    /// 检查是否应该切换策略
    fn check_strategy_switch(&mut self) {
        // 如果使用随机首块策略，检查是否已下载足够的随机首块
        if self.strategy == PieceSelectionStrategy::RandomFirst &&
           self.verified_pieces >= self.random_first_pieces {
            self.strategy = PieceSelectionStrategy::RarestFirst;
            info!("Switching to rarest first strategy after downloading {} random pieces", self.verified_pieces);
        }

        // 检查是否应该进入端游模式
        let missing_pieces = self.total_pieces - self.verified_pieces;
        if missing_pieces <= self.end_game_threshold &&
           self.strategy != PieceSelectionStrategy::EndGame {
            self.strategy = PieceSelectionStrategy::EndGame;
            info!("Entering end game mode with {} pieces remaining", missing_pieces);
        }
    }

    /// 获取当前策略
    pub fn get_strategy(&self) -> PieceSelectionStrategy {
        self.strategy
    }

    /// 设置策略
    pub fn set_strategy(&mut self, strategy: PieceSelectionStrategy) {
        self.strategy = strategy;
    }

    /// 设置总分片数量
    pub fn set_total_pieces(&mut self, total_pieces: u32) {
        self.total_pieces = total_pieces as usize;

        // 检查是否应该切换策略
        self.check_strategy_switch();
    }

    /// 设置是否考虑对等点性能
    pub fn set_consider_peer_performance(&mut self, consider: bool) {
        self.consider_peer_performance = consider;
    }

    /// 更新对等点评分
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `download_speed` - 对等点的下载速度（字节/秒）
    /// * `success` - 是否成功完成请求
    /// * `response_time` - 响应时间（毫秒）
    pub fn update_peer_score(&mut self, peer_addr: SocketAddr, download_speed: u64, success: bool, response_time: u64) {
        let score = self.peer_scores.entry(peer_addr).or_insert_with(PeerScore::new);
        score.update(download_speed, success, response_time);

        debug!("Updated peer score for {}: speed={} B/s, success_rate={:.2}, response_time={}ms, overall={:.2}",
               peer_addr, score.download_speed, score.success_rate, score.response_time, score.overall_score);
    }

    /// 获取对等点评分
    pub fn get_peer_score(&self, peer_addr: &SocketAddr) -> Option<PeerScore> {
        self.peer_scores.get(peer_addr).copied()
    }

    /// 获取所有对等点评分
    pub fn get_all_peer_scores(&self) -> &HashMap<SocketAddr, PeerScore> {
        &self.peer_scores
    }

    /// 选择下一个要下载的分片
    pub fn select_next_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        match self.strategy {
            PieceSelectionStrategy::Sequential =>
                self.select_sequential_piece(peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::RarestFirst =>
                self.select_rarest_piece(peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::RandomFirst =>
                self.select_random_first_piece(peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::EndGame =>
                self.select_end_game_piece(peer_pieces, piece_states),
        }
    }

    /// 为特定对等点选择下一个要下载的分片
    pub fn select_next_piece_for_peer(
        &self,
        peer_addr: &SocketAddr,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        // 如果不考虑对等点性能或没有对等点评分，使用普通选择方法
        if !self.consider_peer_performance || !self.peer_scores.contains_key(peer_addr) {
            return self.select_next_piece(peer_pieces, piece_states, requested_pieces);
        }

        // 获取对等点评分
        let peer_score = self.peer_scores.get(peer_addr).unwrap();

        // 如果对等点评分较低，使用普通选择方法
        if peer_score.overall_score < 0.3 {
            return self.select_next_piece(peer_pieces, piece_states, requested_pieces);
        }

        // 根据策略选择分片
        match self.strategy {
            PieceSelectionStrategy::Sequential =>
                self.select_sequential_piece(peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::RarestFirst =>
                self.select_weighted_rarest_piece(peer_addr, peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::RandomFirst =>
                self.select_random_first_piece(peer_pieces, piece_states, requested_pieces),
            PieceSelectionStrategy::EndGame =>
                self.select_end_game_piece(peer_pieces, piece_states),
        }
    }

    /// 顺序选择分片
    fn select_sequential_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) &&
               !requested_pieces.contains(&piece_index) {
                return Some(piece_index);
            }
        }
        None
    }

    /// 选择最稀有的分片
    fn select_rarest_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        let mut rarest_pieces: Vec<u32> = Vec::new();
        let mut min_rarity = usize::MAX;

        // 找出对方拥有的、我们缺失的、最稀有的分片
        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) &&
               !requested_pieces.contains(&piece_index) {
                let rarity = *self.piece_peer_counts.get(&piece_index).unwrap_or(&0);

                if rarity < min_rarity {
                    min_rarity = rarity;
                    rarest_pieces.clear();
                    rarest_pieces.push(piece_index);
                } else if rarity == min_rarity {
                    rarest_pieces.push(piece_index);
                }
            }
        }

        // 如果有多个同样稀有的分片，随机选择一个
        if !rarest_pieces.is_empty() {
            let index = rand::random::<usize>() % rarest_pieces.len();
            return Some(rarest_pieces[index]);
        }

        None
    }

    /// 选择加权最稀有的分片，考虑对等点性能
    fn select_weighted_rarest_piece(
        &self,
        peer_addr: &SocketAddr,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        // 获取对等点评分
        let peer_score = match self.peer_scores.get(peer_addr) {
            Some(score) => score,
            None => return self.select_rarest_piece(peer_pieces, piece_states, requested_pieces),
        };

        // 收集候选分片
        let mut candidate_pieces: Vec<(u32, f64)> = Vec::new();

        // 找出对方拥有的、我们缺失的分片
        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) &&
               !requested_pieces.contains(&piece_index) {
                let rarity = *self.piece_peer_counts.get(&piece_index).unwrap_or(&0);

                // 计算分片权重
                // 稀有度权重：稀有度越低，权重越高
                let rarity_weight = if rarity == 0 {
                    1.0
                } else {
                    1.0 / (rarity as f64)
                };

                // 对等点性能权重
                let performance_weight = peer_score.overall_score;

                // 综合权重
                let weight = rarity_weight * 0.7 + performance_weight * 0.3;

                candidate_pieces.push((piece_index, weight));
            }
        }

        // 如果没有候选分片，返回None
        if candidate_pieces.is_empty() {
            return None;
        }

        // 按权重排序
        candidate_pieces.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // 返回权重最高的分片
        Some(candidate_pieces[0].0)
    }

    /// 随机选择首块分片
    fn select_random_first_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        // 收集所有对方拥有的、我们缺失的分片
        let mut available_pieces: Vec<u32> = Vec::new();
        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) &&
               !requested_pieces.contains(&piece_index) {
                available_pieces.push(piece_index);
            }
        }

        // 随机选择一个分片
        if !available_pieces.is_empty() {
            let index = rand::random::<usize>() % available_pieces.len();
            return Some(available_pieces[index]);
        }

        None
    }

    /// 端游模式选择分片
    fn select_end_game_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState]
    ) -> Option<u32> {
        // 在端游模式下，我们可以向多个对等点请求同一个分片
        // 这里简单地选择任何对方拥有的、我们缺失的分片
        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) {
                return Some(piece_index);
            }
        }

        None
    }

    /// 智能端游模式选择分片，考虑对等点性能
    fn select_smart_end_game_piece(
        &self,
        peer_addr: &SocketAddr,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        // 获取对等点评分
        let peer_score = match self.peer_scores.get(peer_addr) {
            Some(score) => score,
            None => return self.select_end_game_piece(peer_pieces, piece_states),
        };

        // 如果对等点评分较低，减少请求数量
        if peer_score.overall_score < 0.3 {
            // 只有当请求数量较少时才向低性能对等点请求
            if requested_pieces.len() < 3 {
                return self.select_end_game_piece(peer_pieces, piece_states);
            } else {
                return None;
            }
        }

        // 对于高性能对等点，可以请求更多分片
        let mut missing_pieces = Vec::new();

        for i in 0..piece_states.len() {
            let piece_index = i as u32;
            if piece_states[i] == PieceState::Missing &&
               peer_pieces.contains(&piece_index) {
                missing_pieces.push(piece_index);
            }
        }

        // 如果没有缺失的分片，返回None
        if missing_pieces.is_empty() {
            return None;
        }

        // 随机选择一个分片
        let index = rand::random::<usize>() % missing_pieces.len();
        Some(missing_pieces[index])
    }

    /// 更新对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `peer_pieces` - 对等点的分片
    pub fn update_peer_pieces(&mut self, peer_addr: &SocketAddr, new_peer_pieces: &HashSet<u32>) {
        let old_pieces = self.peer_piece_availability.insert(*peer_addr, new_peer_pieces.clone());

        // 更新稀有度计数
        if let Some(old_pieces) = old_pieces {
            // 移除旧的稀有度计数
            for piece_index in &old_pieces {
                if let Some(count) = self.piece_peer_counts.get_mut(piece_index) {
                    *count -= 1;
                    if *count == 0 {
                        self.piece_peer_counts.remove(piece_index);
                    }
                }
            }
        }

        // 添加新的稀有度计数
        for piece_index in new_peer_pieces {
            *self.piece_peer_counts.entry(*piece_index).or_insert(0) += 1;
        }

        debug!("Updated peer pieces for {}: {} pieces", peer_addr, new_peer_pieces.len());
    }
    
    /// 添加对等点拥有的分片
    /// 
    /// # 参数
    /// * `peer_addr` - 对等点的网络地址
    /// * `piece_index` - 分片索引
    pub fn add_peer_piece(&mut self, peer_addr: &SocketAddr, piece_index: u32) {
        let pieces = self.peer_piece_availability.entry(*peer_addr).or_insert_with(HashSet::new);
        if pieces.insert(piece_index) {
            // 如果是新添加的分片，更新稀有度计数
            *self.piece_peer_counts.entry(piece_index).or_insert(0) += 1;
            debug!("Peer {} now has piece {}. Rarity for piece {} is now {}", 
                   peer_addr, piece_index, piece_index, self.piece_peer_counts[&piece_index]);
        } else {
            debug!("Peer {} already had piece {}", peer_addr, piece_index);
        }
    }
    
    /// 清理过期的对等点评分
    pub fn cleanup_peer_scores(&mut self, max_age: std::time::Duration) {
        let now = Instant::now();
        let mut peers_to_remove = Vec::new();

        // 收集需要移除的对等点
        for (addr, score) in &self.peer_scores {
            // 检查对等点评分是否过期
            if now.duration_since(score.last_update) > max_age {
                debug!("Removing peer score for {} due to inactivity (last update: {:?} ago)",
                       addr, now.duration_since(score.last_update));
                peers_to_remove.push(*addr);
            }
        }

        // 移除过期的对等点评分
        for addr in &peers_to_remove {
            self.peer_scores.remove(addr);
        }

        if !peers_to_remove.is_empty() {
            debug!("Cleaned up {} expired peer scores", peers_to_remove.len());
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};
    use std::thread::sleep;
    use std::time::Duration;

    #[test]
    fn test_peer_score_update() {
        // 创建新的对等点评分
        let mut score = PeerScore::new();

        // 初始值检查
        assert_eq!(score.download_speed, 0);
        assert_eq!(score.success_rate, 1.0);
        assert_eq!(score.response_time, 0);
        assert_eq!(score.overall_score, 0.5);

        // 记录初始更新时间
        let initial_update = score.last_update;

        // 短暂延迟，确保时间戳会变化
        sleep(Duration::from_millis(10));

        // 更新评分
        score.update(1_000_000, true, 100);

        // 检查更新后的值
        assert_eq!(score.download_speed, 1_000_000);
        assert_eq!(score.success_rate, 1.0);
        assert_eq!(score.response_time, 100);
        assert!(score.last_update > initial_update, "Last update timestamp should be updated");

        // 记录第二次更新时间
        let second_update = score.last_update;

        // 短暂延迟，确保时间戳会变化
        sleep(Duration::from_millis(10));

        // 更新评分（失败）
        score.update(500_000, false, 200);

        // 检查更新后的值
        assert_eq!(score.download_speed, 850_000); // (1_000_000 * 7 + 500_000 * 3) / 10
        assert!(score.success_rate < 1.0);
        assert_eq!(score.response_time, 130); // (100 * 7 + 200 * 3) / 10
        assert!(score.last_update > second_update, "Last update timestamp should be updated again");
    }

    #[test]
    fn test_piece_selector_basic() {
        // 创建分片选择器
        let mut selector = PieceSelector::new(
            PieceSelectionStrategy::RarestFirst,
            4, // 端游模式阈值
            4, // 随机首块数量
            100 // 总分片数量
        );

        // 检查初始策略
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RarestFirst);

        // 更新已验证的分片数量
        selector.update_verified_pieces(96);

        // 检查是否切换到端游模式
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::EndGame);
    }

    #[test]
    fn test_weighted_rarest_piece_selection() {
        // 创建分片选择器
        let mut selector = PieceSelector::new(
            PieceSelectionStrategy::RarestFirst,
            4, // 端游模式阈值
            4, // 随机首块数量
            10 // 总分片数量
        );

        // 创建对等点地址
        let peer_addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);

        // 更新对等点评分
        selector.update_peer_score(peer_addr, 1_000_000, true, 50);

        // 创建对等点拥有的分片
        let mut peer_pieces = HashSet::new();
        peer_pieces.insert(1);
        peer_pieces.insert(2);
        peer_pieces.insert(3);

        // 创建分片状态
        let piece_states = vec![
            PieceState::Verified, // 0
            PieceState::Missing,  // 1
            PieceState::Missing,  // 2
            PieceState::Missing,  // 3
        ];

        // 创建分片稀有度


        // 创建已请求的分片
        let requested_pieces = HashSet::new();

        // 选择分片
        let piece = selector.select_weighted_rarest_piece(
            &peer_addr,
            &peer_pieces,
            &piece_states,
            &requested_pieces
        );

        // 应该选择稀有度最低的分片（2）
        assert_eq!(piece, Some(2));
    }

    #[test]
    fn test_cleanup_peer_scores() {
        // 创建分片选择器
        let mut selector = PieceSelector::new(
            PieceSelectionStrategy::RarestFirst,
            4, // 端游模式阈值
            4, // 随机首块数量
            10 // 总分片数量
        );

        // 创建对等点地址
        let peer_addr1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer_addr2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);
        let peer_addr3 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8082);

        // 更新对等点评分
        selector.update_peer_score(peer_addr1, 1_000_000, true, 50);
        selector.update_peer_score(peer_addr2, 500_000, true, 100);
        selector.update_peer_score(peer_addr3, 2_000_000, true, 30);

        // 验证初始状态
        assert_eq!(selector.get_all_peer_scores().len(), 3);
        assert!(selector.get_peer_score(&peer_addr1).is_some());
        assert!(selector.get_peer_score(&peer_addr2).is_some());
        assert!(selector.get_peer_score(&peer_addr3).is_some());

        // 手动修改第一个对等点的最后更新时间，使其过期
        if let Some(score) = selector.peer_scores.get_mut(&peer_addr1) {
            // 将最后更新时间设置为很久以前
            score.last_update = Instant::now() - Duration::from_secs(3600); // 1小时前
        }

        // 清理过期的对等点评分（设置超时为10分钟）
        selector.cleanup_peer_scores(Duration::from_secs(600));

        // 验证清理结果
        assert_eq!(selector.get_all_peer_scores().len(), 2);
        assert!(selector.get_peer_score(&peer_addr1).is_none(), "Peer 1 should be removed");
        assert!(selector.get_peer_score(&peer_addr2).is_some(), "Peer 2 should still exist");
        assert!(selector.get_peer_score(&peer_addr3).is_some(), "Peer 3 should still exist");

        // 手动修改所有对等点的最后更新时间，使其过期
        for score in selector.peer_scores.values_mut() {
            score.last_update = Instant::now() - Duration::from_secs(3600); // 1小时前
        }

        // 再次清理
        selector.cleanup_peer_scores(Duration::from_secs(600));

        // 验证所有对等点都被清理
        assert_eq!(selector.get_all_peer_scores().len(), 0);
    }
}
