//! 对等点管理模块
//! 
//! 这个模块负责管理 BitTorrent 协议中的对等点连接、消息处理、请求管理和片段选择。
//! 它被重构为多个子模块，每个子模块负责特定的功能，以提高代码的可维护性和可测试性。

pub mod connection_manager;
pub use crate::protocols::bittorrent::peer_manager::connection_manager::ConnectionManager as PeerConnectionManager;
use crate::protocols::bittorrent::peer_manager::connection_manager::ConnectionManager;
mod message_processor;
mod request_manager;
mod piece_selection_manager;
mod dht_integration;

pub use message_processor::MessageProcessor;
pub use request_manager::RequestManager;
pub use piece_selection_manager::PieceSelectionManager;
pub use dht_integration::DHTIntegration;

use std::collections::{HashMap, VecDeque, HashSet};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::{<PERSON>tex, RwLock};
use std::time::{Duration, Instant};

use tracing::{debug, warn};
use crate::core::Peer;
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::peer_quality::{PeerQualityManager, PeerQualityConfig};
use crate::protocols::bittorrent::peer_statistics::PeerStatistics;
use crate::protocols::bittorrent::piece_manager::PieceSelectionStrategy;
use crate::protocols::bittorrent::security::{SecurityManager, SecurityConfig};
use crate::protocols::bittorrent::dht_manager::DHTManager;
use crate::protocols::bittorrent::extension_manager::ExtensionManager;
use crate::protocols::bittorrent::traits::peer_manager_trait::PeerManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use crate::protocols::bittorrent::tracker::TrackerResponse;
use crate::protocols::bittorrent::torrent::TorrentInfo;

type Result<T> = std::result::Result<T, BitTorrentError>;

/// 对等点管理器
/// 
/// 这个结构体是对等点管理系统的核心，它协调各个组件的工作，
/// 包括连接管理、消息处理、请求管理、片段选择和 DHT 集成。
pub struct PeerManager {
    /// 连接管理器，负责管理对等点的连接
    connection_manager: ConnectionManager,
    /// 分片管理器，用于获取分片状态
    piece_manager: Arc<RwLock<dyn crate::core::p2p::piece::PieceManager + Send + Sync>>,
    
    /// 消息处理器，负责处理来自对等点的消息
    message_processor: MessageProcessor,
    
    /// 请求管理器，负责管理对等点的请求
    request_manager: RequestManager,
    
    /// 片段选择管理器，负责选择要下载的片段
    piece_selection_manager: PieceSelectionManager,
    
    /// DHT 集成，负责与 DHT 网络交互
    dht_integration: Option<DHTIntegration>,
    
    /// 对等点统计信息
    peer_statistics: Arc<RwLock<PeerStatistics>>,
    
    /// 对等点质量管理器
    quality_manager: Arc<RwLock<PeerQualityManager>>,
    
    /// 安全管理器
    security_manager: Option<Arc<tokio::sync::RwLock<SecurityManager>>>,
    
    /// 扩展管理器
    extension_manager: Option<Arc<RwLock<ExtensionManager>>>,
    
    /// 本地对等点ID
    local_peer_id: Vec<u8>,
    
    /// 种子信息哈希
    info_hash: Option<Vec<u8>>,
    
    /// 最后清理时间
    last_cleanup: Instant,
}

impl PeerManager {
    /// 创建新的对等点管理器
    pub fn new(
        local_peer_id: String,
        connect_timeout: u64,
        initial_strategy: PieceSelectionStrategy,
        num_pieces: usize,
        dht_manager: Option<Arc<DHTManager>>,
        piece_manager: Arc<RwLock<dyn crate::core::p2p::piece::PieceManager + Send + Sync>>,
    ) -> Self {
        // 创建对等点统计信息
        let peer_statistics = Arc::new(RwLock::new(PeerStatistics::new()));
        
        // 创建安全管理器
        let security_manager = Arc::new(tokio::sync::RwLock::new(
            SecurityManager::new(SecurityConfig::default())
        ));
        
        // 创建对等点质量管理器
        let config = PeerQualityConfig::default();
        let quality_manager = Arc::new(RwLock::new(PeerQualityManager::new(
            config,
            security_manager.clone(),
            None
        )));
        
        // 创建连接管理器
        let connection_manager = ConnectionManager::new(
            local_peer_id.as_bytes().to_vec(),
            50, // 默认最大对等点数量
            Some(security_manager.clone()),
            quality_manager.clone(),
            num_pieces.try_into().unwrap(),
            dht_manager.clone(),
            connect_timeout,
        );
        
        // 创建消息处理器
        let message_processor = MessageProcessor::new(quality_manager.clone());
        
        // 创建请求管理器
        let request_manager = RequestManager::new();
        
        // 创建片段选择管理器
        let piece_selection_manager = PieceSelectionManager::new(
            initial_strategy,
            10, // end_game_threshold 默认值
            4,  // random_first_pieces 默认值
            100 // total_pieces 默认值，实际应该根据种子信息动态设置
        );
        
        Self {
            connection_manager,
            message_processor,
            request_manager,
            piece_selection_manager,
            dht_integration: None,
            peer_statistics,
            quality_manager,
            security_manager: Some(security_manager),
            extension_manager: None,
            local_peer_id: local_peer_id.as_bytes().to_vec(),
            info_hash: None,
            last_cleanup: Instant::now(),
            piece_manager,
        }
    }
    
    /// 设置DHT管理器
    pub fn set_dht_manager(&mut self, dht_manager: Option<Arc<DHTManager>>) {
        if let Some(dht) = dht_manager.clone() {
            // 将 Arc<DHTManager> 包装在 Arc<Mutex<DHTManager>> 中
            let dht_mutex = Arc::new(Mutex::new((*dht).clone()));
            self.dht_integration = Some(DHTIntegration::new(dht_mutex));
        } else {
            self.dht_integration = None;
        }
    }
    
    /// 设置种子信息哈希
    pub fn set_info_hash(&mut self, info_hash: Vec<u8>) {
        self.info_hash = Some(info_hash.clone());
        self.connection_manager.set_info_hash(info_hash.clone());
        self.message_processor.set_info_hash(info_hash.clone());
        self.request_manager.set_info_hash(info_hash.clone());
        if let Some(dht) = &mut self.dht_integration {
            dht.set_info_hash(info_hash);
        }
    }

    /// 获取活跃对等点的数量
    pub async fn active_peers(&self) -> u64 {
        self.connection_manager.get_connected_peer_count() as u64
    }

    /// 处理新的对等点列表
    pub async fn process_peers(&mut self, peers: Vec<SocketAddr>) -> Result<()> {
        debug!("处理 {} 个新对等点", peers.len());
        self.connection_manager.process_peers(peers).await?;
        // 更新对等点统计信息
        let mut stats = self.peer_statistics.write().await;
        stats.update_total_peers(self.connection_manager.get_queue_length() as usize);
        stats.update_active_peers(self.connection_manager.get_connected_peer_count() as usize);
        Ok(())
    }
    
    /// 连接对等点
    pub async fn connect_peers(&mut self) -> Result<()> {
        self.connection_manager.process_peer_queue().await?;
        // 更新对等点统计信息
        let mut stats = self.peer_statistics.write().await;
        stats.update_total_peers(self.connection_manager.get_queue_length() as usize);
        stats.update_active_peers(self.connection_manager.get_connected_peer_count() as usize);
        Ok(())
    }
    
    /// 移除失败的对等点
    pub async fn remove_failed_peers(&mut self) -> Result<()> {
        self.connection_manager.remove_failed_peers().await?;
        // 更新对等点统计信息
        let mut stats = self.peer_statistics.write().await;
        stats.update_active_peers(self.connection_manager.get_connected_peer_count() as usize);
        Ok(())
    }
    
    /// 处理对等点消息
    pub async fn process_messages(&mut self) -> Result<()> {
        // 获取所有已连接的对等点
        let peers = self.connection_manager.get_peers().clone();
        
        // 使用消息处理器处理消息
        self.message_processor.process_messages(&peers).await?;
        
        Ok(())
    }
    
    /// 发送请求
    pub async fn send_requests(&mut self) -> Result<()> {
        // 获取所有已连接的对等点
        let peers = self.connection_manager.get_peers().clone();
        
        // 获取分片状态
        let piece_states = self.get_piece_states().await?;
        // 获取已请求的分片
        let requested_pieces = self.request_manager.get_requested_pieces().clone();
        // 使用片段选择管理器选择要下载的片段
        let selected_pieces = self.piece_selection_manager.select_pieces(
            &peers,
            &piece_states,
            &requested_pieces
        ).await?;
        
        // 使用请求管理器发送下载请求
        self.request_manager.send_download_requests(
            &peers,
            &selected_pieces,
            16384 // 默认块大小为 16KB
        ).await?;
        
        // 处理上传请求
        self.request_manager.process_upload_requests(&peers).await?;
        
        // 检查请求超时
        self.request_manager.check_timeouts();
        
        Ok(())
    }
    
    /// 更新对等点统计信息
    pub async fn update_peer_statistics(&mut self) -> Result<()> {
        // 获取所有已连接的对等点
        let peers = self.connection_manager.get_peers().clone();
        for (addr, peer) in &peers {
            let peer = peer.lock().await;
            // 获取下载速度
            let download_speed = peer.download_speed();
            // 获取上传速度
            let upload_speed = peer.upload_speed();
            // 这里移除 get_response_time/get_success_rate，保持简洁
            let mut manager = self.quality_manager.write().await;
            if download_speed < 1024 {
                manager.record_violation(
                    addr,
                    "low_download_speed",
                    "下载速度过低",
                    1,
                ).await;
            }
            if upload_speed < 1024 {
                manager.record_violation(
                    addr,
                    "low_upload_speed",
                    "上传速度过低",
                    1,
                ).await;
            }
            // 可根据需要添加其它统计项
        }
        Ok(())
    }
    
    /// 查询 DHT 对等点
    pub async fn query_dht_peers(&mut self) -> Result<()> {
        // 检查 DHT 集成是否可用
        if let Some(dht) = &mut self.dht_integration {
            // 查询对等点
            let peers = dht.query_peers().await?;
            
            // 处理查询到的对等点
            if !peers.is_empty() {
                self.process_peers(peers).await?;
            }
        }
        
        Ok(())
    }
    
    /// 初始化扩展协议
    pub async fn init_extensions(&mut self) -> Result<()> {
        // 创建扩展管理器
        let mut extension_manager = ExtensionManager::new(true); // 启用扩展
        
        // 创建对等点获取函数
        let peers_clone = self.connection_manager.get_peers().clone();
        let peer_getter = Arc::new(move || {
            peers_clone.keys().cloned().collect()
        });
        
        // 创建对等点添加函数
        let self_weak = Arc::downgrade(&Arc::new(Mutex::new(self.clone())));
        let peer_adder = Arc::new(move |addr: SocketAddr| -> Result<()> {
            if let Some(peer_manager) = self_weak.upgrade() {
                tokio::spawn(async move {
                    if let Err(e) = peer_manager.lock().await.process_peers(vec![addr]).await {
                        warn!("Failed to add peer from extension: {}", e);
                    }
                });
                Ok(())
            } else {
                Err(BitTorrentError::Other(format!("Failed to upgrade peer_manager weak reference")))
            }
        });
        
        // 初始化扩展
        extension_manager.init_extensions(peer_getter, peer_adder).await?;
        
        // 保存扩展管理器
        self.extension_manager = Some(Arc::new(RwLock::new(extension_manager)));
        
        debug!("Extension protocol initialized");
        Ok(())
    }
    
    /// 初始化元数据交换扩展
    pub async fn init_metadata_exchange(&mut self, info_hash: Vec<u8>) -> Result<()> {
        // 设置info_hash
        self.set_info_hash(info_hash.clone());
        
        // 检查扩展管理器是否已初始化
        if self.extension_manager.is_none() {
            debug!("Extension manager not initialized, initializing...");
            self.init_extensions().await?;
        }
        
        // 获取扩展管理器
        if let Some(extension_manager) = &self.extension_manager {
            let mut manager = extension_manager.write().await;
            
            // 注册元数据交换扩展
            let mut info_hash_array = [0u8; 20];
            if info_hash.len() >= 20 {
                info_hash_array.copy_from_slice(&info_hash[0..20]);
            } else {
                return Err(BitTorrentError::Other(format!("Invalid info_hash length: {}", info_hash.len())));
            }
            
            // 注册元数据交换扩展
            manager.register_metadata_extension(info_hash_array).await?;
            debug!("Metadata exchange extension initialized with info_hash: {}", hex::encode(info_hash_array));
        } else {
            return Err(BitTorrentError::Other("Failed to initialize extension manager".to_string()));
        }
        
        Ok(())
    }
    
    /// 获取分片状态
    /// 
    /// # 实现说明
    /// 当前 PeerManager 未直接持有 PieceManager，无法获取实际分片状态。
    /// 推荐后续将 PieceManager 注入 PeerManager 或通过 ConnectionManager 间接访问。
    /// 目前返回一个包含所有分片初始状态（Missing）的向量作为占位，
    /// 仅表示分片总数，不反映实际下载进度或对等点拥有情况。
    /// 真正的分片状态需要 PieceManager 提供。
    pub async fn get_piece_states(&self) -> Result<Vec<PieceState>> {
        let piece_manager = self.piece_manager.read().await;
        piece_manager.all_piece_states().await
    }
    
    /// 获取对等点质量管理器
    pub fn quality_manager(&self) -> &Arc<RwLock<PeerQualityManager>> {
        &self.quality_manager
    }

    /// 获取可变的对等点质量管理器
    pub fn quality_manager_mut(&mut self) -> &mut Arc<RwLock<PeerQualityManager>> {
        &mut self.quality_manager
    }
    
    /// 克隆对等点管理器
    pub fn clone(&self) -> Self {
        Self {
            connection_manager: ConnectionManager::new(
                self.local_peer_id.clone(),
                self.connection_manager.max_peers(),
                self.security_manager.clone(),
                self.quality_manager.clone(),
                self.connection_manager.num_pieces(),
                self.connection_manager.dht_manager(),
                self.connection_manager.peer_handshake_timeout_secs(),
            ),
            message_processor: MessageProcessor::new(self.quality_manager.clone()),
            request_manager: RequestManager::new(),
            piece_selection_manager: PieceSelectionManager::new(
                PieceSelectionStrategy::RarestFirst, // 默认策略
                10, // end_game_threshold 默认值
                4,  // random_first_pieces 默认值
                100 // total_pieces 默认值
            ),
            dht_integration: self.dht_integration.clone(),
            peer_statistics: self.peer_statistics.clone(),
            quality_manager: self.quality_manager.clone(),
            piece_manager: self.piece_manager.clone(),
            security_manager: self.security_manager.clone(),
            extension_manager: self.extension_manager.clone(),
            local_peer_id: self.local_peer_id.clone(),
            info_hash: self.info_hash.clone(),
            last_cleanup: Instant::now(),
        }
    }
    
    /// 获取当前连接的对等点数量
    pub fn connected_peer_count(&self) -> usize {
        self.connection_manager.get_connected_peer_count()
    }
    /// 获取对等点队列长度
    pub fn peer_queue_length(&self) -> usize {
        self.connection_manager.get_queue_length()
    }
    
    /// 设置最大对等点数量
    pub fn set_max_peers(&mut self, max_peers: usize) {
        self.connection_manager.set_max_peers(max_peers);
    }
}

use crate::protocols::bittorrent::extensions::extension_message_handler::PeerManagerCallback;
use async_trait::async_trait;

#[async_trait]
impl PeerManagerCallback for Mutex<PeerManager> {
    fn is_peer_connected(&self, addr: &SocketAddr) -> bool {
        // 只读锁即可
        let guard = self.try_lock();
        if let Ok(pm) = guard {
            pm.connection_manager.get_peers().contains_key(addr)
        } else {
            false
        }
    }
    async fn add_peer_to_queue(&self, addr: SocketAddr) -> Result<()> {
        let mut pm = self.lock().await;
        pm.process_peers(vec![addr]).await
    }
}

#[async_trait::async_trait]
impl PeerManagerTrait for PeerManager {
    /// 设置最大对等点数量
    async fn set_max_peers(&mut self, max: usize) -> Result<()> {
        self.connection_manager.set_max_peers(max);
        Ok(())
    }

    /// 从 Tracker 响应中添加对等点
    async fn add_peers_from_tracker(&mut self, response: TrackerResponse, torrent_info: &TorrentInfo) -> Result<()> {
        debug!("处理来自 Tracker 的 {} 个对等点", response.peers.len());
        let peers_to_add = response.peers.into_iter()
            .map(|p| SocketAddr::new(p.ip, p.port))
            .collect();
        self.connection_manager.process_peers(peers_to_add).await.map_err(BitTorrentError::from)
    }

    /// 获取所有已连接的对等点
    async fn get_peers(&self) -> Result<HashMap<SocketAddr, Arc<Mutex<dyn Peer>>>> {
        Ok(self.connection_manager.get_peers().clone())
    }

    // ...根据需要实现 PeerManagerTrait 中的其他方法...
}