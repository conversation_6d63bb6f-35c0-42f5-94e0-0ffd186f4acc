use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::{debug, warn};

use crate::protocols::bittorrent::extensions::message::{ExtensionHandshake, ExtensionMessageTrait};
use crate::protocols::bittorrent::message::BitTorrentMessage;

/// 扩展协议处理器
pub struct ExtensionHandler {
    /// 是否支持扩展协议
    pub supports_extensions: bool,
    /// 对方支持的扩展映射 (扩展名 -> 消息ID)
    pub extension_map: HashMap<String, u8>,
    /// 客户端版本
    pub client_version: Option<String>,
    /// 元数据大小
    pub metadata_size: Option<u64>,
    /// PEX回调函数，用于通知PeerManager添加新的对等点
    pub pex_callback: Option<Arc<dyn Fn(SocketAddr) -> Result<()> + Send + Sync>>,
}

impl ExtensionHandler {
    /// 创建新的扩展协议处理器
    pub fn new(supports_extensions: bool) -> Self {
        Self {
            supports_extensions,
            extension_map: HashMap::new(),
            client_version: None,
            metadata_size: None,
            pex_callback: None,
        }
    }

    /// 设置PEX回调函数
    pub fn set_pex_callback(&mut self, callback: Arc<dyn Fn(SocketAddr) -> Result<()> + Send + Sync>) {
        self.pex_callback = Some(callback);
    }

    /// 生成扩展握手消息
    pub fn generate_handshake(&self) -> Result<BitTorrentMessage> {
        // 如果不支持扩展协议，返回错误
        if !self.supports_extensions {
            return Err(anyhow!("Extension protocol not supported"));
        }

        // 创建扩展握手消息
        let mut handshake = ExtensionHandshake::new();

        // 添加支持的扩展
        handshake.add_extension("ut_pex", 1); // PEX扩展

        // 设置客户端版本
        handshake.version = Some("Tonitru/0.1.0".to_string());

        // 编码握手消息
        let payload = handshake.encode()?;

        // 返回扩展握手消息
        Ok(BitTorrentMessage::Extended(0, payload))
    }

    /// 处理扩展消息
    pub async fn process_message(&mut self, ext_id: u8, payload: &[u8]) -> Result<()> {
        // 处理扩展握手消息
        if ext_id == 0 {
            return self.process_handshake(payload).await;
        }

        // 查找对应的扩展处理器
        match ext_id {
            1 => {
                // PEX扩展
                debug!("Received PEX message");

                // 解析PEX消息
                match crate::protocols::bittorrent::extensions::pex::PEXMessage::decode(payload) {
                    Ok(pex_message) => {
                        // 处理PEX消息
                        self.process_pex_message(pex_message).await?;
                    },
                    Err(e) => {
                        warn!("Failed to decode PEX message: {}", e);
                    }
                }
            },
            _ => {
                debug!("Received unknown extension message with ID {}", ext_id);
            }
        }

        Ok(())
    }

    /// 处理扩展握手消息
    async fn process_handshake(&mut self, payload: &[u8]) -> Result<()> {
        // 解码扩展握手消息
        let handshake = ExtensionHandshake::decode(payload)?;

        // 更新扩展映射
        self.extension_map = handshake.extensions;

        // 更新客户端版本
        self.client_version = handshake.version;

        // 更新元数据大小
        self.metadata_size = handshake.metadata_size;

        debug!("Processed extension handshake: {:?}", self.extension_map);

        Ok(())
    }

    /// 处理PEX消息
    async fn process_pex_message(&self, message: crate::protocols::bittorrent::extensions::pex::PEXMessage) -> Result<()> {
        debug!("Processing PEX message with {} IPv4 peers and {} IPv6 peers",
               message.added.len() / 6, message.added6.len() / 18);

        // 解析IPv4对等点
        let mut added_peers = Vec::new();

        // 每个IPv4对等点占6字节
        if message.added.len() % 6 == 0 {
            for i in 0..(message.added.len() / 6) {
                let offset = i * 6;

                // 解析IP地址
                let ip = std::net::Ipv4Addr::new(
                    message.added[offset],
                    message.added[offset + 1],
                    message.added[offset + 2],
                    message.added[offset + 3],
                );

                // 解析端口
                let port = u16::from_be_bytes([message.added[offset + 4], message.added[offset + 5]]);

                // 创建套接字地址
                let addr = std::net::SocketAddr::new(std::net::IpAddr::V4(ip), port);
                added_peers.push(addr);
            }
        } else {
            warn!("Invalid PEX added peers data length: {}", message.added.len());
        }

        // 解析IPv6对等点
        if message.added6.len() % 18 == 0 {
            for i in 0..(message.added6.len() / 18) {
                let offset = i * 18;

                // 解析IP地址
                let mut ip_bytes = [0u8; 16];
                ip_bytes.copy_from_slice(&message.added6[offset..offset + 16]);
                let ip = std::net::Ipv6Addr::from(ip_bytes);

                // 解析端口
                let port = u16::from_be_bytes([message.added6[offset + 16], message.added6[offset + 17]]);

                // 创建套接字地址
                let addr = std::net::SocketAddr::new(std::net::IpAddr::V6(ip), port);
                added_peers.push(addr);
            }
        } else if !message.added6.is_empty() {
            warn!("Invalid PEX added6 peers data length: {}", message.added6.len());
        }

        // 通知PeerManager添加新发现的对等点
        if !added_peers.is_empty() {
            debug!("PEX: Discovered {} new peers", added_peers.len());

            // 如果设置了PEX回调函数，使用它来添加新的对等点
            if let Some(callback) = &self.pex_callback {
                for addr in added_peers {
                    // 获取对等点地址的字符串表示
                    let addr_str = addr.to_string();
                    debug!("PEX: Adding peer {}", addr_str);

                    // 调用回调函数添加新的对等点
                    match callback(addr) {
                        Ok(_) => debug!("PEX: Successfully added peer {}", addr),
                        Err(e) => warn!("PEX: Failed to add peer {}: {}", addr, e),
                    }
                }
            } else {
                debug!("PEX: No callback registered, cannot add discovered peers");
            }
        }

        Ok(())
    }

    /// 检查对方是否支持指定的扩展
    pub fn supports_extension(&self, extension_name: &str) -> bool {
        // 首先检查是否支持扩展协议
        if !self.supports_extensions {
            return false;
        }

        // 检查扩展映射中是否包含指定的扩展名
        self.extension_map.contains_key(extension_name)
    }
}
