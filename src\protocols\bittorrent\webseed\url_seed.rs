use async_trait::async_trait;
use bytes::Bytes;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::time::{Duration, Instant};

use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::utils::HttpClient;

use super::error::{WebSeedError, WebSeedResult};
use super::performance::WebSeedPerformance;
use super::webseed_source::{WebSeedSourceTrait, WebSeedType};

/// URL Seed (BEP 19 GetRight风格)
#[derive(Clone)]
pub struct UrlSeed {
    /// 基础URL
    url: String,
    /// HTTP客户端
    http_client: HttpClient,
    /// 种子信息
    torrent_info: Arc<TorrentInfo>,
    /// 性能指标
    performance: WebSeedPerformance,
    /// 文件路径映射缓存
    file_path_cache: HashMap<usize, (usize, u64)>,
}

impl UrlSeed {
    /// 创建新的URL Seed
    pub fn new(url: String, http_client: HttpClient, torrent_info: Arc<TorrentInfo>) -> WebSeedResult<Self> {
        // 确保URL以斜杠结尾
        let url = if url.ends_with('/') {
            url
        } else {
            format!("{}/", url)
        };

        Ok(Self {
            url,
            http_client,
            torrent_info,
            performance: WebSeedPerformance::default(),
            file_path_cache: HashMap::new(),
        })
    }

    /// 获取分片对应的文件和偏移量
    fn get_file_for_piece(&self, piece_index: usize) -> WebSeedResult<(usize, u64)> {
        // 检查缓存
        if let Some(file_info) = self.file_path_cache.get(&piece_index) {
            return Ok(*file_info);
        }

        let piece_offset = piece_index as u64 * self.torrent_info.piece_length;

        // 获取文件列表
        let files = self.torrent_info.files.as_ref()
            .ok_or_else(|| WebSeedError::Other("No files in torrent info".to_string()))?;

        // 单文件种子
        if files.len() == 1 {
            // 不再更新缓存，因为我们没有可变引用
            return Ok((0, piece_offset));
        }

        // 多文件种子
        let mut current_offset = 0u64;
        for (i, file) in files.iter().enumerate() {
            let next_offset = current_offset + file.length;

            if piece_offset < next_offset {
                // 找到了包含此分片的文件
                let file_offset = piece_offset - current_offset;
                // 不再更新缓存，因为我们没有可变引用
                return Ok((i, file_offset));
            }

            current_offset = next_offset;
        }

        Err(WebSeedError::InvalidPieceIndex(piece_index))
    }

    /// 构建文件URL
    fn build_file_url(&self, file_index: usize) -> WebSeedResult<String> {
        // 获取文件列表
        let files = self.torrent_info.files.as_ref()
            .ok_or_else(|| WebSeedError::Other("No files in torrent info".to_string()))?;

        if file_index >= files.len() {
            return Err(WebSeedError::InvalidFileIndex(file_index));
        }

        let file = &files[file_index];

        // 单文件种子
        if files.len() == 1 {
            let file_name = Path::new(&file.path).file_name()
                .ok_or_else(|| WebSeedError::Other(format!("Invalid file path: {}", file.path)))?
                .to_string_lossy();
            return Ok(format!("{}{}", self.url, file_name));
        }

        // 多文件种子
        let path = file.path.replace('\\', "/");
        Ok(format!("{}{}", self.url, path))
    }
}

#[async_trait]
impl WebSeedSourceTrait for UrlSeed {
    fn url(&self) -> &str {
        &self.url
    }

    fn source_type(&self) -> WebSeedType {
        WebSeedType::UrlSeed
    }

    async fn download_piece(&self, piece_index: usize, _piece_length: usize,
                           piece_offset: usize, length: usize) -> WebSeedResult<Bytes> {
        let start_time = Instant::now();

        // 获取分片对应的文件和偏移量
        let (file_index, file_offset) = self.get_file_for_piece(piece_index)?;

        // 构建文件URL
        let file_url = self.build_file_url(file_index)?;

        // 计算范围
        let range_start = file_offset + piece_offset as u64;
        let range_end = range_start + length as u64 - 1;

        // 创建带范围的请求
        let mut req = self.http_client.inner().get(&file_url);
        req = req.header(reqwest::header::RANGE, format!("bytes={}-{}", range_start, range_end));

        // 发送请求
        let response = req.send().await.map_err(|e| WebSeedError::HttpError(e.to_string()))?;

        // 检查响应状态
        if !response.status().is_success() && response.status() != reqwest::StatusCode::PARTIAL_CONTENT {
            return Err(WebSeedError::HttpError(format!("Failed to download piece: HTTP {}", response.status())));
        }

        // 读取响应体
        let data = response.bytes().await.map_err(|e| WebSeedError::HttpError(e.to_string()))?;

        // 验证数据长度
        if data.len() != length {
            return Err(WebSeedError::DataLengthMismatch {
                expected: length,
                actual: data.len(),
            });
        }

        // 更新性能指标
        let download_time = start_time.elapsed();
        let mut this = self.clone();
        this.update_performance(download_time, data.len(), true);

        Ok(data)
    }

    async fn is_available(&self) -> bool {
        // 检查基础URL是否可访问
        match self.http_client.head(&self.url).await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }

    fn performance(&self) -> &WebSeedPerformance {
        &self.performance
    }

    fn performance_mut(&mut self) -> &mut WebSeedPerformance {
        &mut self.performance
    }

    fn update_performance(&mut self, download_time: Duration,
                         bytes_downloaded: usize, success: bool) {
        self.performance.update(download_time, bytes_downloaded, success);
    }

    fn priority(&self) -> f64 {
        self.performance.calculate_priority()
    }

    fn clone_box(&self) -> Box<dyn WebSeedSourceTrait> {
        Box::new(self.clone())
    }
}
