use std::sync::Arc;
use std::convert::TryInto;
use tokio::sync::Mutex;
use tokio::time::Duration;

use crate::core::p2p::protocol::DownloadStats;
use crate::core::p2p::piece::PieceManager;
use super::torrent::TorrentInfo;
use super::peer_manager::PeerManager;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use crate::protocols::bittorrent::traits::{PieceManagerTrait, PeerManagerTrait};

type Result<T> = std::result::Result<T, BitTorrentError>;

#[async_trait::async_trait]
pub trait StatsManagerTrait: Send + Sync {
    async fn progress(&self) -> f64;
    async fn download_speed(&self) -> u64;
    async fn update_stats(
        &mut self,
        piece_manager: Option<&std::sync::Arc<tokio::sync::Mutex<dyn PieceManagerTrait>>>,
        torrent_info: Option<&TorrentInfo>,
        peer_manager: &dyn PeerManagerTrait,
        downloaded: u64,
        uploaded: u64
    ) -> Result<()>;
    async fn get_stats(&self) -> DownloadStats;
}

/// 统计信息管理器，负责收集和更新统计信息
#[derive(Clone)]
pub struct StatsManager {
    /// 下载统计信息
    stats: DownloadStats,
    /// 上次更新时间
    last_update: std::time::Instant,
    /// 上次下载量
    last_downloaded: u64,
    /// 上次上传量
    last_uploaded: u64,
    /// 更新间隔（毫秒）
    update_interval: u64,
}

impl StatsManager {
    /// 创建新的统计信息管理器
    pub fn new(update_interval: u64) -> Self {
        Self {
            stats: DownloadStats {
                downloaded: 0,
                uploaded: 0,
                download_speed: 0,
                upload_speed: 0,
                connected_peers: 0,
                total_peers: 0,
                pieces_downloaded: 0,
                pieces_total: 0,
                progress: 0.0,
                estimated_time: None,
            },
            last_update: std::time::Instant::now(),
            last_downloaded: 0,
            last_uploaded: 0,
            update_interval,
        }
    }

    /// 更新统计信息
    pub async fn update_stats(
        &mut self,
        piece_manager: Option<&Arc<Mutex<dyn PieceManager>>>,
        torrent_info: Option<&TorrentInfo>,
        peer_manager: &PeerManager,
        downloaded: u64,
        uploaded: u64
    ) -> Result<()> {
        // 计算下载速度和上传速度
        let now = std::time::Instant::now();
        let elapsed = now.duration_since(self.last_update).as_millis() as u64;

        if elapsed >= self.update_interval {
            // 计算下载速度（字节/秒）
            let download_diff = downloaded.saturating_sub(self.last_downloaded);
            let upload_diff = uploaded.saturating_sub(self.last_uploaded);

            self.stats.download_speed = (download_diff * 1000) / elapsed;
            self.stats.upload_speed = (upload_diff * 1000) / elapsed;

            // 更新上次下载量和上传量
            self.last_downloaded = downloaded;
            self.last_uploaded = uploaded;

            // 更新上次更新时间
            self.last_update = now;
        }

        // 更新总下载量和上传量
        self.stats.downloaded = downloaded;
        self.stats.uploaded = uploaded;

        // 更新对等点统计信息
        self.stats.connected_peers = peer_manager.active_peers().await;
        self.stats.total_peers = peer_manager.total_peers().await;

        // 如果有分片管理器，更新分片统计信息
        if let Some(piece_manager) = piece_manager {
            let piece_manager = piece_manager.lock().await;

            if let Ok(progress) = piece_manager.progress().await {
                self.stats.progress = progress;
            }

            if let Ok(downloaded_size) = piece_manager.downloaded_size().await {
                // 估算已下载的分片数量
                if let Some(torrent_info) = torrent_info {
                    let piece_size = torrent_info.piece_length;
                    if piece_size > 0 {
                        self.stats.pieces_downloaded = (downloaded_size / piece_size) as u32;
                    }
                }
            }
        }

        // 如果有种子信息，更新总分片数
        if let Some(torrent_info) = torrent_info {
            let num_pieces = (torrent_info.total_size + torrent_info.piece_length - 1) / torrent_info.piece_length;
            self.stats.pieces_total = num_pieces as u32;

            // 使用已下载的字节数估算进度
            if torrent_info.total_size > 0 && self.stats.progress == 0.0 {
                self.stats.progress = self.stats.downloaded as f64 / torrent_info.total_size as f64 * 100.0;
            }

            // 估算剩余时间
            if self.stats.download_speed > 0 {
                let remaining_bytes = torrent_info.total_size.saturating_sub(self.stats.downloaded);
                if remaining_bytes > 0 {
                    let seconds = remaining_bytes / self.stats.download_speed;
                    self.stats.estimated_time = Some(Duration::from_secs(seconds));
                } else {
                    self.stats.estimated_time = Some(Duration::from_secs(0));
                }
            } else {
                self.stats.estimated_time = None;
            }
        }

        Ok(())
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> DownloadStats {
        self.stats.clone()
    }

    /// 获取下载进度
    pub fn progress(&self) -> f64 {
        self.stats.progress
    }

    /// 获取下载速度
    pub fn download_speed(&self) -> u64 {
        self.stats.download_speed
    }

    /// 获取上传速度
    pub fn upload_speed(&self) -> u64 {
        self.stats.upload_speed
    }

    /// 获取已下载大小
    pub fn downloaded(&self) -> u64 {
        self.stats.downloaded
    }

    /// 获取已上传大小
    pub fn uploaded(&self) -> u64 {
        self.stats.uploaded
    }

    /// 获取已下载分片数量
    pub fn pieces_downloaded(&self) -> u32 {
        self.stats.pieces_downloaded
    }

    /// 获取总分片数量
    pub fn pieces_total(&self) -> u32 {
        self.stats.pieces_total
    }

    /// 获取估计剩余时间
    pub fn estimated_time(&self) -> Option<Duration> {
        self.stats.estimated_time
    }

    /// 获取连接的对等点数量
    pub fn connected_peers(&self) -> u32 {
        self.stats.connected_peers.try_into().unwrap_or(0)
    }

    /// 获取总对等点数量
    pub fn total_peers(&self) -> u32 {
        self.stats.total_peers.try_into().unwrap_or(0)
    }

    /// 设置已上传大小
    pub fn set_uploaded(&mut self, uploaded: u64) {
        self.stats.uploaded = uploaded;
        self.last_uploaded = uploaded;
    }
}
