use async_trait::async_trait;
use bytes::Bytes;
use std::time::Duration;

use super::error::WebSeedResult;
use super::performance::WebSeedPerformance;

/// WebSeed源类型
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum WebSeedType {
    /// BEP 19 (GetRight风格) URL Seed
    UrlSeed,
    /// BEP 17 (Hoffman风格) HTTP Seed
    HttpSeed,
}

/// WebSeed源接口
#[async_trait]
pub trait WebSeedSourceTrait: Send + Sync {
    /// 获取源URL
    fn url(&self) -> &str;

    /// 获取源类型（HTTP或URL）
    fn source_type(&self) -> WebSeedType;

    /// 从此WebSeed源下载一个分片
    async fn download_piece(&self, piece_index: usize, piece_length: usize,
                           piece_offset: usize, length: usize) -> WebSeedResult<Bytes>;

    /// 检查此源是否可用
    async fn is_available(&self) -> bool;

    /// 获取此源的性能指标
    fn performance(&self) -> &WebSeedPerformance;

    /// 获取此源的可变性能指标
    fn performance_mut(&mut self) -> &mut WebSeedPerformance;

    /// 更新性能指标
    fn update_performance(&mut self, download_time: Duration,
                         bytes_downloaded: usize, success: bool);

    /// 获取源优先级（基于性能指标计算）
    fn priority(&self) -> f64;

    /// 克隆源
    fn clone_box(&self) -> Box<dyn WebSeedSourceTrait>;
}
