use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncSeekExt, AsyncWriteExt, SeekFrom};
use tokio::sync::RwLock;
use tracing::{debug, warn, error};
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use crate::protocols::bittorrent::file_manager::FileManager;
use super::piece_data::Piece;

/// 分片存储配置
#[derive(Debug, Clone)]
pub struct PieceStorageConfig {
    /// 是否启用写入缓存
    pub enable_write_cache: bool,
    /// 写入缓存大小（字节）
    pub write_cache_size: usize,
    /// 是否启用异步写入
    pub enable_async_write: bool,
    /// 写入超时时间（毫秒）
    pub write_timeout_ms: u64,
    /// 是否在写入后立即同步到磁盘
    pub sync_after_write: bool,
}

impl Default for PieceStorageConfig {
    fn default() -> Self {
        Self {
            enable_write_cache: true,
            write_cache_size: 16 * 1024 * 1024, // 16MB
            enable_async_write: true,
            write_timeout_ms: 5000, // 5秒
            sync_after_write: false,
        }
    }
}

/// 写入任务
#[derive(Debug)]
struct WriteTask {
    /// 分片索引
    piece_index: u32,
    /// 分片数据
    data: Vec<u8>,
    /// 文件偏移量
    file_offset: u64,
    /// 任务创建时间
    created_at: std::time::Instant,
}

/// 分片存储统计信息
#[derive(Debug, Clone, Default)]
pub struct StorageStats {
    /// 已写入的分片数量
    pub pieces_written: u64,
    /// 写入失败的分片数量
    pub write_failures: u64,
    /// 总写入字节数
    pub bytes_written: u64,
    /// 平均写入时间（毫秒）
    pub average_write_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
}

impl StorageStats {
    /// 更新写入统计
    pub fn update_write(&mut self, bytes: u64, time_ms: u64, success: bool) {
        if success {
            self.pieces_written += 1;
            self.bytes_written += bytes;
            
            let total_writes = self.pieces_written as f64;
            self.average_write_time_ms = 
                (self.average_write_time_ms * (total_writes - 1.0) + time_ms as f64) / total_writes;
        } else {
            self.write_failures += 1;
        }
    }

    /// 获取缓存命中率
    pub fn cache_hit_rate(&self) -> f64 {
        let total = self.cache_hits + self.cache_misses;
        if total == 0 {
            0.0
        } else {
            self.cache_hits as f64 / total as f64
        }
    }
}

/// 分片存储管理器
/// 
/// 负责管理分片数据的存储，包括文件写入、缓存管理等
pub struct PieceStorageManager {
    /// 配置
    config: PieceStorageConfig,
    /// 文件管理器
    file_manager: Arc<RwLock<FileManager>>,
    /// 写入缓存 (piece_index -> data)
    write_cache: HashMap<u32, Vec<u8>>,
    /// 当前缓存大小
    cache_size: usize,
    /// 待写入任务队列
    write_queue: Vec<WriteTask>,
    /// 统计信息
    stats: StorageStats,
}

impl PieceStorageManager {
    /// 创建新的分片存储管理器
    pub fn new(config: PieceStorageConfig, file_manager: Arc<RwLock<FileManager>>) -> Self {
        Self {
            config,
            file_manager,
            write_cache: HashMap::new(),
            cache_size: 0,
            write_queue: Vec::new(),
            stats: StorageStats::default(),
        }
    }

    /// 创建默认的分片存储管理器
    pub fn with_defaults(file_manager: Arc<RwLock<FileManager>>) -> Self {
        Self::new(PieceStorageConfig::default(), file_manager)
    }

    /// 存储分片数据
    pub async fn store_piece(&mut self, piece: &Piece) -> Result<(), BitTorrentError> {
        if piece.state != PieceState::Verified {
            return Err(BitTorrentError::from("Cannot store unverified piece"));
        }

        let start_time = std::time::Instant::now();
        let piece_data = piece.get_data()?;
        let piece_size = piece_data.len() as u64;

        // 计算文件偏移量
        let file_offset = self.calculate_file_offset(piece.index, piece.size).await?;

        if self.config.enable_write_cache {
            // 使用缓存存储
            self.store_with_cache(piece.index, piece_data, file_offset).await?;
        } else {
            // 直接写入文件
            self.write_to_file(piece.index, &piece_data, file_offset).await?;
        }

        // 更新统计信息
        let write_time = start_time.elapsed().as_millis() as u64;
        self.stats.update_write(piece_size, write_time, true);

        debug!("Stored piece {} ({} bytes) at offset {} in {}ms", 
               piece.index, piece_size, file_offset, write_time);

        Ok(())
    }

    /// 使用缓存存储分片
    async fn store_with_cache(
        &mut self,
        piece_index: u32,
        data: Vec<u8>,
        file_offset: u64,
    ) -> Result<(), BitTorrentError> {
        let data_size = data.len();

        // 检查缓存是否已满
        if self.cache_size + data_size > self.config.write_cache_size {
            // 刷新缓存
            self.flush_cache().await?;
        }

        // 添加到缓存
        self.write_cache.insert(piece_index, data);
        self.cache_size += data_size;
        self.stats.cache_hits += 1;

        // 如果启用异步写入，添加到写入队列
        if self.config.enable_async_write {
            let task = WriteTask {
                piece_index,
                data: self.write_cache[&piece_index].clone(),
                file_offset,
                created_at: std::time::Instant::now(),
            };
            self.write_queue.push(task);
        }

        Ok(())
    }

    /// 刷新写入缓存
    pub async fn flush_cache(&mut self) -> Result<(), BitTorrentError> {
        if self.write_cache.is_empty() {
            return Ok(());
        }

        debug!("Flushing write cache ({} pieces, {} bytes)", 
               self.write_cache.len(), self.cache_size);

        // 处理写入队列
        for task in self.write_queue.drain(..) {
            if let Some(data) = self.write_cache.remove(&task.piece_index) {
                self.write_to_file(task.piece_index, &data, task.file_offset).await?;
                self.cache_size = self.cache_size.saturating_sub(data.len());
            }
        }

        // 清空剩余缓存
        self.write_cache.clear();
        self.cache_size = 0;

        Ok(())
    }

    /// 写入文件
    async fn write_to_file(
        &mut self,
        piece_index: u32,
        data: &[u8],
        file_offset: u64,
    ) -> Result<(), BitTorrentError> {
        let file_manager = self.file_manager.read().await;
        
        // 获取目标文件信息
        let file_info = file_manager.get_file_for_offset(file_offset)
            .ok_or_else(|| BitTorrentError::from("No file found for offset"))?;

        // 计算在文件中的偏移量
        let file_local_offset = file_offset - file_info.offset;

        // 打开文件
        let mut file = OpenOptions::new()
            .write(true)
            .create(true)
            .open(&file_info.path)
            .await
            .map_err(|e| BitTorrentError::from(format!("Failed to open file: {}", e)))?;

        // 定位到正确位置
        file.seek(SeekFrom::Start(file_local_offset))
            .await
            .map_err(|e| BitTorrentError::from(format!("Failed to seek file: {}", e)))?;

        // 写入数据
        file.write_all(data)
            .await
            .map_err(|e| BitTorrentError::from(format!("Failed to write data: {}", e)))?;

        // 如果配置要求，同步到磁盘
        if self.config.sync_after_write {
            file.sync_all()
                .await
                .map_err(|e| BitTorrentError::from(format!("Failed to sync file: {}", e)))?;
        }

        debug!("Wrote piece {} to file {} at offset {}", 
               piece_index, file_info.path.display(), file_local_offset);

        Ok(())
    }

    /// 计算分片在文件中的偏移量
    async fn calculate_file_offset(&self, piece_index: u32, piece_size: u64) -> Result<u64, BitTorrentError> {
        // 这里应该根据种子文件信息计算实际的文件偏移量
        // 简化实现：假设分片是连续的
        Ok(piece_index as u64 * piece_size)
    }

    /// 处理超时的写入任务
    pub async fn handle_write_timeouts(&mut self) -> Result<(), BitTorrentError> {
        let timeout_duration = std::time::Duration::from_millis(self.config.write_timeout_ms);
        let now = std::time::Instant::now();
        
        let mut timed_out_tasks = Vec::new();
        self.write_queue.retain(|task| {
            if now.duration_since(task.created_at) > timeout_duration {
                timed_out_tasks.push(task.piece_index);
                false
            } else {
                true
            }
        });

        for piece_index in timed_out_tasks {
            warn!("Write task for piece {} timed out", piece_index);
            self.stats.write_failures += 1;
            
            // 从缓存中移除
            if let Some(data) = self.write_cache.remove(&piece_index) {
                self.cache_size = self.cache_size.saturating_sub(data.len());
            }
        }

        Ok(())
    }

    /// 批量存储分片
    pub async fn store_pieces(&mut self, pieces: &[Piece]) -> Result<Vec<u32>, BitTorrentError> {
        let mut stored_pieces = Vec::new();
        
        for piece in pieces {
            if piece.state == PieceState::Verified {
                match self.store_piece(piece).await {
                    Ok(()) => stored_pieces.push(piece.index),
                    Err(e) => {
                        error!("Failed to store piece {}: {}", piece.index, e);
                        self.stats.write_failures += 1;
                    }
                }
            }
        }
        
        Ok(stored_pieces)
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> &StorageStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = StorageStats::default();
    }

    /// 获取缓存使用情况
    pub fn get_cache_usage(&self) -> (usize, usize, f64) {
        let usage_percent = if self.config.write_cache_size > 0 {
            (self.cache_size as f64 / self.config.write_cache_size as f64) * 100.0
        } else {
            0.0
        };
        
        (self.cache_size, self.config.write_cache_size, usage_percent)
    }

    /// 获取待写入任务数量
    pub fn get_pending_write_count(&self) -> usize {
        self.write_queue.len()
    }

    /// 更新配置
    pub fn update_config(&mut self, config: PieceStorageConfig) {
        self.config = config;
    }

    /// 清空缓存和队列
    pub async fn clear(&mut self) -> Result<(), BitTorrentError> {
        self.flush_cache().await?;
        self.write_queue.clear();
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_storage_stats() {
        let mut stats = StorageStats::default();
        
        // 测试写入统计
        stats.update_write(1024, 100, true);
        stats.update_write(2048, 150, true);
        stats.update_write(512, 200, false);
        
        assert_eq!(stats.pieces_written, 2);
        assert_eq!(stats.write_failures, 1);
        assert_eq!(stats.bytes_written, 3072);
        assert_eq!(stats.average_write_time_ms, 125.0); // (100 + 150) / 2
    }

    #[test]
    fn test_cache_hit_rate() {
        let mut stats = StorageStats::default();
        
        stats.cache_hits = 8;
        stats.cache_misses = 2;
        
        assert_eq!(stats.cache_hit_rate(), 0.8);
    }
}
