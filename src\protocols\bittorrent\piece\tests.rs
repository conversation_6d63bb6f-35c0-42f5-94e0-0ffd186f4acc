#[cfg(test)]
mod integration_tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use std::collections::HashSet;
    use std::thread::sleep;
    use std::time::Duration;
    use crate::core::p2p::piece::PieceState;

    #[test]
    fn test_piece_selector_integration() {
        // 创建分片选择器
        let mut selector = PieceSelectorFactory::create_for_testing(10);

        // 检查初始策略
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RandomFirst);

        // 更新已验证的分片数量，触发策略切换
        selector.update_verified_pieces(6);

        // 检查是否切换到端游模式
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::EndGame);
    }

    #[test]
    fn test_weighted_rarest_piece_selection_integration() {
        // 创建分片选择器
        let mut selector = PieceSelectorFactory::create_custom(
            PieceSelectionStrategy::RarestFirst,
            4, // 端游模式阈值
            4, // 随机首块数量
            10, // 总分片数量
            true, // 考虑对等点性能
        );

        // 创建对等点地址
        let peer_addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);

        // 更新对等点评分
        selector.update_peer_score(peer_addr, 1_000_000, true, 50);

        // 创建对等点拥有的分片
        let mut peer_pieces = HashSet::new();
        peer_pieces.insert(1);
        peer_pieces.insert(2);
        peer_pieces.insert(3);

        // 更新对等点分片信息
        selector.update_peer_pieces(&peer_addr, &peer_pieces);

        // 创建分片状态
        let piece_states = vec![
            PieceState::Verified, // 0
            PieceState::Missing,  // 1
            PieceState::Missing,  // 2
            PieceState::Missing,  // 3
        ];

        // 创建已请求的分片
        let requested_pieces = HashSet::new();

        // 选择分片
        let piece = selector.select_next_piece_for_peer(
            &peer_addr,
            &peer_pieces,
            &piece_states,
            &requested_pieces
        );

        // 应该选择一个可用的分片
        assert!(piece.is_some());
        let selected_piece = piece.unwrap();
        assert!(peer_pieces.contains(&selected_piece));
        assert_eq!(piece_states[selected_piece as usize], PieceState::Missing);
    }

    #[test]
    fn test_peer_management_integration() {
        let mut selector = PieceSelectorFactory::create_default();
        
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);

        // 添加对等点分片
        selector.add_peer_piece(&peer1, 0);
        selector.add_peer_piece(&peer1, 1);
        selector.add_peer_piece(&peer2, 1);
        selector.add_peer_piece(&peer2, 2);

        // 检查分片稀有度
        assert_eq!(selector.get_piece_rarity(0), 1); // 只有peer1有
        assert_eq!(selector.get_piece_rarity(1), 2); // peer1和peer2都有
        assert_eq!(selector.get_piece_rarity(2), 1); // 只有peer2有

        // 检查拥有分片的对等点
        let peers_with_piece1 = selector.get_peers_with_piece(1);
        assert_eq!(peers_with_piece1.len(), 2);
        assert!(peers_with_piece1.contains(&peer1));
        assert!(peers_with_piece1.contains(&peer2));

        // 移除对等点
        selector.remove_peer(&peer1);
        assert_eq!(selector.get_piece_rarity(0), 0); // peer1被移除后，分片0的稀有度变为0
        assert_eq!(selector.get_piece_rarity(1), 1); // 只剩peer2有分片1
    }

    #[test]
    fn test_strategy_switching() {
        let mut selector = PieceSelectorFactory::create_custom(
            PieceSelectionStrategy::RandomFirst,
            5, // 端游模式阈值
            3, // 随机首块数量
            100, // 总分片数量
            true,
        );

        // 初始策略应该是RandomFirst
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RandomFirst);

        // 更新已验证分片数量，但还没达到随机首块阈值
        selector.update_verified_pieces(2);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RandomFirst);

        // 达到随机首块阈值，应该切换到RarestFirst
        selector.update_verified_pieces(3);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::RarestFirst);

        // 达到端游模式阈值，应该切换到EndGame
        selector.update_verified_pieces(96); // 100 - 96 = 4 < 5
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::EndGame);
    }

    #[test]
    fn test_peer_score_cleanup_integration() {
        let mut selector = PieceSelectorFactory::create_default();
        
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);

        // 更新对等点评分
        selector.update_peer_score(peer1, 1_000_000, true, 50);
        selector.update_peer_score(peer2, 500_000, true, 100);

        // 验证评分存在
        assert!(selector.get_peer_score(&peer1).is_some());
        assert!(selector.get_peer_score(&peer2).is_some());

        // 清理过期评分（使用很短的超时时间）
        sleep(Duration::from_millis(10));
        selector.cleanup_expired_peer_scores(Duration::from_millis(5));

        // 验证评分被清理
        assert!(selector.get_peer_score(&peer1).is_none());
        assert!(selector.get_peer_score(&peer2).is_none());
    }

    #[test]
    fn test_factory_configurations() {
        // 测试不同工厂方法创建的选择器
        let quick_start = PieceSelectorFactory::create_for_quick_start(1000);
        assert_eq!(quick_start.get_strategy(), PieceSelectionStrategy::RandomFirst);

        let sequential = PieceSelectorFactory::create_for_sequential_download(1000);
        assert_eq!(sequential.get_strategy(), PieceSelectionStrategy::Sequential);

        let high_perf = PieceSelectorFactory::create_for_high_performance(1000);
        assert_eq!(high_perf.get_strategy(), PieceSelectionStrategy::RarestFirst);

        let low_resource = PieceSelectorFactory::create_for_low_resource(1000);
        assert_eq!(low_resource.get_strategy(), PieceSelectionStrategy::Sequential);
        assert!(!low_resource.is_considering_peer_performance());
    }

    #[test]
    fn test_selector_stats() {
        let mut selector = PieceSelectorFactory::create_for_testing(100);
        
        let peer1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let peer2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8081);

        // 添加一些数据
        selector.update_peer_score(peer1, 1_000_000, true, 50);
        selector.add_peer_piece(&peer1, 0);
        selector.add_peer_piece(&peer1, 1);
        selector.add_peer_piece(&peer2, 1);
        selector.add_peer_piece(&peer2, 2);
        selector.update_verified_pieces(10);

        // 获取统计信息
        let stats = selector.get_stats();
        assert_eq!(stats.current_strategy, PieceSelectionStrategy::RandomFirst);
        assert_eq!(stats.verified_pieces, 10);
        assert_eq!(stats.total_pieces, 100);
        assert_eq!(stats.peer_count, 2);
        assert_eq!(stats.peer_score_count, 1);
        assert_eq!(stats.known_piece_count, 3);
        assert_eq!(stats.total_piece_instances, 4); // 0:1, 1:2, 2:1
    }

    #[test]
    fn test_config_update() {
        let mut selector = PieceSelectorFactory::create_default();
        
        // 获取初始配置
        let initial_config = selector.get_config().clone();
        assert_eq!(initial_config.initial_strategy, PieceSelectionStrategy::RarestFirst);

        // 更新配置
        let new_config = PieceSelectorConfig {
            initial_strategy: PieceSelectionStrategy::Sequential,
            end_game_threshold: 10,
            random_first_pieces: 2,
            total_pieces: 500,
            consider_peer_performance: false,
        };

        selector.update_config(new_config.clone());

        // 验证配置更新
        let updated_config = selector.get_config();
        assert_eq!(updated_config.initial_strategy, new_config.initial_strategy);
        assert_eq!(updated_config.end_game_threshold, new_config.end_game_threshold);
        assert_eq!(updated_config.total_pieces, new_config.total_pieces);
        assert_eq!(selector.get_strategy(), PieceSelectionStrategy::Sequential);
        assert!(!selector.is_considering_peer_performance());
    }

    #[test]
    fn test_piece_selection_with_requested_pieces() {
        let mut selector = PieceSelectorFactory::create_custom(
            PieceSelectionStrategy::Sequential,
            5, 0, 10, true
        );

        let peer_addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        
        // 对等点拥有分片0, 1, 2
        let mut peer_pieces = HashSet::new();
        peer_pieces.insert(0);
        peer_pieces.insert(1);
        peer_pieces.insert(2);
        selector.update_peer_pieces(&peer_addr, &peer_pieces);

        // 分片状态：0已验证，1和2缺失
        let piece_states = vec![
            PieceState::Verified, // 0
            PieceState::Missing,  // 1
            PieceState::Missing,  // 2
        ];

        // 没有已请求的分片
        let requested_pieces = HashSet::new();
        let piece = selector.select_next_piece(&peer_pieces, &piece_states, &requested_pieces);
        assert_eq!(piece, Some(1)); // 应该选择分片1（顺序选择，跳过已验证的分片0）

        // 分片1已被请求
        let mut requested_pieces = HashSet::new();
        requested_pieces.insert(1);
        let piece = selector.select_next_piece(&peer_pieces, &piece_states, &requested_pieces);
        assert_eq!(piece, Some(2)); // 应该选择分片2
    }
}
