use crate::protocols::bittorrent::block_manager::{BlockAddedCallback, PieceCompleteCallback};
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

#[async_trait::async_trait]
pub trait BlockManagerTrait: Send + Sync {
    async fn set_piece_complete_callback(&mut self, callback: Arc<Mutex<dyn PieceCompleteCallback>>);
    async fn set_block_added_callback(&mut self, callback: Arc<dyn BlockAddedCallback>);
    async fn check_timeouts(&mut self) -> Vec<u32>;
    // ...可扩展其它接口...
}
