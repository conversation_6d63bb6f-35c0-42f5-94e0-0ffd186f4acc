//! 对等点质量管理器实现

use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use anyhow::Result;
use tracing::{debug, info, warn};

use crate::protocols::bittorrent::security::SecurityManager;
use crate::protocols::bittorrent::peer_quality::models::{EnhancedPeerScore, PeerQualityConfig};
use crate::protocols::bittorrent::peer_quality::filters::{PeerFilterTrait, GeoLocationServiceTrait, BlacklistFilter, PerformanceFilter, GeoFilter};

/// 对等点质量管理器
pub struct PeerQualityManager {
    /// 配置
    config: PeerQualityConfig,
    /// 对等点评分
    peer_scores: HashMap<SocketAddr, EnhancedPeerScore>,
    /// 过滤器
    filters: Vec<Arc<dyn PeerFilterTrait>>,
    /// 安全管理器
    security_manager: Arc<tokio::sync::RwLock<SecurityManager>>,
    /// 地理位置服务
    geo_service: Option<Arc<dyn GeoLocationServiceTrait>>,
    /// 最后清理时间
    last_cleanup: Instant,
}

impl PeerQualityManager {
    /// 创建新的对等点质量管理器
    pub fn new(
        config: PeerQualityConfig,
        security_manager: Arc<tokio::sync::RwLock<SecurityManager>>,
        geo_service: Option<Arc<dyn GeoLocationServiceTrait>>
    ) -> Self {
        let mut manager = Self {
            config,
            peer_scores: HashMap::new(),
            filters: Vec::new(),
            security_manager,
            geo_service,
            last_cleanup: Instant::now(),
        };
        
        // 添加默认过滤器
        manager.add_filter(Arc::new(BlacklistFilter::new(manager.security_manager.clone())));
        
        // 添加性能过滤器
        if let Ok(threshold) = manager.config.filter_params.get("performance_threshold").unwrap_or(&"0.3".to_string()).parse::<f64>() {
            manager.add_filter(Arc::new(PerformanceFilter::new(threshold)));
        }
        
        // 如果有地理位置服务，添加地理位置过滤器
        if manager.geo_service.is_some() {
            // 这里可以从配置中读取允许的国家/地区列表
            let allowed_countries = vec!["CN".to_string(), "US".to_string(), "JP".to_string()];
            manager.add_filter(Arc::new(GeoFilter::new(manager.geo_service.clone(), allowed_countries)));
        }
        
        manager
    }
    
    /// 设置地理位置服务
    pub fn set_geo_service(&mut self, geo_service: Option<Arc<dyn GeoLocationServiceTrait>>) {
        self.geo_service = geo_service;
        
        // 更新过滤器
        let mut new_filters = Vec::new();
        
        // 保留非地理位置过滤器
        for filter in self.filters.drain(..) {
            if filter.name() != "GeoFilter" {
                new_filters.push(filter);
            }
        }
        
        // 重新添加过滤器
        self.filters = new_filters;
        
        // 如果有地理位置服务，添加地理位置过滤器
        if self.geo_service.is_some() {
            // 这里可以从配置中读取允许的国家/地区列表
            let allowed_countries = vec!["CN".to_string(), "US".to_string(), "JP".to_string()];
            self.add_filter(Arc::new(GeoFilter::new(self.geo_service.clone(), allowed_countries)));
        }
    }
    
    /// 添加过滤器
    pub fn add_filter(&mut self, filter: Arc<dyn PeerFilterTrait>) {
        self.filters.push(filter);
    }
    
    /// 更新对等点评分
    pub fn update_peer_score(
        &mut self,
        addr: &SocketAddr,
        download_speed: Option<u64>,
        upload_speed: Option<u64>,
        success: Option<bool>,
        response_time: Option<u64>
    ) {
        // 获取或创建对等点评分
        let score = self.peer_scores.entry(*addr).or_insert_with(EnhancedPeerScore::new);
        
        // 计算地理位置评分
        let geo_score = if let Some(geo_service) = &self.geo_service {
            Some(geo_service.get_geo_score(addr))
        } else {
            None
        };
        
        // 更新评分
        score.update(
            download_speed,
            upload_speed,
            success,
            response_time,
            None, // 连接稳定性
            geo_score,
            None, // 协议兼容性
        );
        
        // 检查是否需要清理过期评分
        self.check_cleanup();
    }
    
    /// 获取对等点评分
    pub fn get_peer_score(&self, addr: &SocketAddr) -> Option<&EnhancedPeerScore> {
        self.peer_scores.get(addr)
    }
    
    /// 过滤对等点
    pub async fn filter_peer(&self, addr: &SocketAddr) -> Result<bool> {
        let score = self.peer_scores.get(addr);
        
        // 应用所有过滤器
        for filter in &self.filters {
            match filter.filter(addr, score).await {
                Ok(false) => return Ok(false),
                Ok(true) => continue,
                Err(e) => {
                    warn!("过滤器执行失败: {}, 默认允许对等点: {}", e, addr);
                    continue; // 过滤器失败时默认允许
                }
            }
        }
        
        Ok(true)
    }
    
    /// 获取过滤后的对等点列表
    pub async fn get_filtered_peers(&self, peers: &[SocketAddr]) -> Result<Vec<SocketAddr>> {
        let mut filtered_peers = Vec::new();
        
        for addr in peers {
            match self.filter_peer(addr).await {
                Ok(true) => filtered_peers.push(*addr),
                Ok(false) => continue,
                Err(e) => {
                    warn!("过滤对等点失败: {}, 跳过对等点: {}", e, addr);
                    continue;
                }
            }
        }
        
        Ok(filtered_peers)
    }
    
    /// 记录违规行为
    pub async fn record_violation(&mut self, addr: &SocketAddr, violation_type: &str, description: &str, severity: u8) {
        // 获取或创建对等点评分
        let score = self.peer_scores.entry(*addr).or_insert_with(EnhancedPeerScore::new);
        
        // 添加违规记录
        score.add_violation(violation_type, description, severity);
        
        // 如果违规严重，可以考虑添加到黑名单
        if severity >= 8 {
            let reason = format!("严重违规: {}", description);
            let duration = Some(Duration::from_secs(86400)); // 24小时
            let mut manager = self.security_manager.write().await;
            if let Err(e) = manager.add_to_blacklist(addr, &reason, duration).await {
                warn!("添加对等点到黑名单失败: {}", e);
            }
        }
    }
    
    /// 检查是否需要清理过期评分
    fn check_cleanup(&mut self) {
        let now = Instant::now();
        let cleanup_interval = Duration::from_secs(3600); // 1小时
        
        if now.duration_since(self.last_cleanup) > cleanup_interval {
            self.cleanup_expired_scores();
            self.last_cleanup = now;
        }
    }
    
    /// 清理过期的评分
    fn cleanup_expired_scores(&mut self) {
        let max_age = Duration::from_secs(86400); // 24小时
        let mut expired_addrs = Vec::new();
        
        // 收集过期的评分
        for (addr, score) in &self.peer_scores {
            if score.is_expired(max_age) {
                expired_addrs.push(*addr);
            }
        }
        
        // 移除过期的评分
        for addr in &expired_addrs {
            self.peer_scores.remove(addr);
        }
        
        if !expired_addrs.is_empty() {
            debug!("清理了 {} 个过期的对等点评分", expired_addrs.len());
        }
    }
}