pub mod block_manager_trait;
pub mod piece_manager_trait;
pub mod peer_manager_trait;
pub mod tracker_manager_trait;
pub mod dht_manager_trait;
pub mod extension_manager_trait;
pub mod stats_manager_trait;

// Re-export traits for easier access
pub use block_manager_trait::BlockManagerTrait;
pub use piece_manager_trait::PieceManagerTrait;
pub use peer_manager_trait::PeerManagerTrait;
pub use tracker_manager_trait::TrackerManagerTrait;
pub use dht_manager_trait::DHTManagerTrait;
pub use extension_manager_trait::ExtensionManagerTrait;
pub use stats_manager_trait::StatsManagerTrait;
