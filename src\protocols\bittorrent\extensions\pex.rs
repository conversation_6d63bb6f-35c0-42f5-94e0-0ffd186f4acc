use std::collections::{HashSet, HashMap};
use std::net::{IpAddr, SocketAddr, Ipv4Addr, Ipv6Addr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use serde::{Serialize, Deserialize};
use serde_bencode::{to_bytes, from_bytes};
use tracing::{debug, warn};
use async_trait::async_trait;
use hex;

use super::extension_protocol::ExtensionHandler;
use super::message::{ExtensionMessageTrait, ExtensionMessageType};
use crate::protocols::bittorrent::utils::error::BitTorrentError;

// PEX 对等点标志
const PEX_FLAG_PREFER_ENCRYPTION: u8 = 0x01;
const PEX_FLAG_SEED: u8 = 0x02;
const PEX_FLAG_SUPPORTS_UTP: u8 = 0x04;
const PEX_FLAG_REACHABLE: u8 = 0x08;

/// PEX消息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PEXMessage {
    /// 添加的IPv4对等点 (每6字节一个: 4字节IP + 2字节端口)
    #[serde(rename = "added")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub added: Vec<u8>,

    /// 添加的IPv4对等点标志 (每个对等点1字节)
    #[serde(rename = "added.f")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub added_f: Vec<u8>,

    /// 添加的IPv6对等点 (每18字节一个: 16字节IP + 2字节端口)
    #[serde(rename = "added6")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub added6: Vec<u8>,

    /// 添加的IPv6对等点标志 (每个对等点1字节)
    #[serde(rename = "added6.f")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub added6_f: Vec<u8>,

    /// 删除的IPv4对等点 (每6字节一个: 4字节IP + 2字节端口)
    #[serde(rename = "dropped")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub dropped: Vec<u8>,

    /// 删除的IPv6对等点 (每18字节一个: 16字节IP + 2字节端口)
    #[serde(rename = "dropped6")]
    #[serde(with = "serde_bytes")]
    #[serde(default)]
    pub dropped6: Vec<u8>,
}

impl ExtensionMessageTrait for PEXMessage {
    fn message_type(&self) -> ExtensionMessageType {
        ExtensionMessageType::PEX
    }

    fn encode(&self) -> Result<Vec<u8>, crate::protocols::bittorrent::utils::error::BitTorrentError> {
        match to_bytes(self) {
            Ok(data) => Ok(data),
            Err(e) => Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other(format!("Failed to encode PEX message: {}", e))),
        }
    }

    fn decode(data: &[u8]) -> Result<Self, crate::protocols::bittorrent::utils::error::BitTorrentError> where Self: Sized {
        match from_bytes(data) {
            Ok(message) => Ok(message),
            Err(e) => Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other(format!("Failed to decode PEX message: {}", e))),
        }
    }
}

/// PEX扩展实现
pub struct PEXExtension {
    /// 已知对等点集合
    known_peers: Arc<Mutex<HashSet<SocketAddr>>>,
    /// 上次交换时间
    last_exchange: Arc<Mutex<Instant>>,
    /// 交换间隔
    exchange_interval: Duration,
    /// 消息ID
    message_id: u8,
    /// 对等点获取函数
    peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>,
    /// 对等点添加函数
    peer_adder: Arc<dyn Fn(SocketAddr) -> Result<(), BitTorrentError> + Send + Sync>,
    /// 待添加的对等点队列
    pending_peers: Option<Arc<Mutex<Vec<SocketAddr>>>>,
    /// 对等点与info_hash的映射关系
    peer_info_hash_map: Arc<Mutex<HashMap<SocketAddr, Vec<[u8; 20]>>>>,
}

impl PEXExtension {
    /// 创建新的PEX扩展
    pub fn new(
        peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>,
        peer_adder: Arc<dyn Fn(SocketAddr) -> Result<(), BitTorrentError> + Send + Sync>,
    ) -> Self {
        Self {
            known_peers: Arc::new(Mutex::new(HashSet::new())),
            last_exchange: Arc::new(Mutex::new(Instant::now())),
            exchange_interval: Duration::from_secs(60), // 默认1分钟交换一次
            message_id: 1, // 默认消息ID
            peer_getter,
            peer_adder,
            pending_peers: None,
            peer_info_hash_map: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 设置待添加的对等点队列
    pub fn set_pending_peers(&mut self, pending_peers: Arc<Mutex<Vec<SocketAddr>>>) {
        self.pending_peers = Some(pending_peers);
    }

    /// 获取待添加的对等点队列
    pub fn get_pending_peers(&self) -> Option<Arc<Mutex<Vec<SocketAddr>>>> {
        self.pending_peers.clone()
    }

    /// 获取与特定info_hash关联的待添加对等点队列
    /// 
    /// # 参数
    /// * `info_hash` - 种子的信息哈希
    /// 
    /// # 返回
    /// 与指定info_hash相关的待处理对等点列表，如果没有相关对等点则返回所有待处理对等点
    pub fn get_pending_peers_by_info_hash(&self, info_hash: &[u8]) -> Option<Vec<SocketAddr>> {
        // 首先尝试获取所有待处理的对等点
        let pending_peers = self.get_pending_peers()?;
        let pending_peers = pending_peers.try_lock().ok()?;
        
        // 尝试获取info_hash映射
        if let Ok(info_hash_map) = self.peer_info_hash_map.try_lock() {
            // 创建一个新的集合来存储与指定的info_hash相关的对等点
            let mut filtered_peers = Vec::new();
            
            // 将info_hash转换为固定大小的数组
            let mut info_hash_array = [0u8; 20];
            if info_hash.len() == 20 {
                info_hash_array.copy_from_slice(info_hash);
                
                // 遍历所有待处理的对等点
                for peer in pending_peers.iter() {
                    // 检查对等点是否与指定的info_hash相关联
                    if let Some(info_hashes) = info_hash_map.get(peer) {
                        if info_hashes.contains(&info_hash_array) {
                            filtered_peers.push(*peer);
                        }
                    }
                }
                
                // 如果找到了与指定info_hash相关的对等点，则返回这些对等点
                if !filtered_peers.is_empty() {
                    return Some(filtered_peers);
                }
            }
        }
        
        // 如果没有找到与指定info_hash相关的对等点，则返回所有待处理的对等点
        Some(pending_peers.clone())
    }
    
    /// 添加对等点与info_hash的关联
    /// 
    /// # 参数
    /// * `peer` - 对等点地址
    /// * `info_hash` - 种子的信息哈希
    pub async fn add_peer_with_info_hash(&self, peer: SocketAddr, info_hash: [u8; 20]) {
        let mut map = self.peer_info_hash_map.lock().await;
        map.entry(peer)
           .or_insert_with(Vec::new)
           .push(info_hash);
    }

    /// 设置交换间隔
    pub fn set_exchange_interval(&mut self, interval: Duration) {
        self.exchange_interval = interval;
    }

    /// 更新对等点获取函数
    pub fn update_peer_getter(&mut self, peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>) {
        self.peer_getter = peer_getter;
    }

    /// 检查是否应该交换对等点信息
    pub async fn should_exchange(&self) -> bool {
        let last_exchange = *self.last_exchange.lock().await;
        last_exchange.elapsed() >= self.exchange_interval
    }

    /// 生成PEX消息
    pub async fn generate_pex_message(&self) -> Result<PEXMessage, BitTorrentError> {
        let mut known_peers = self.known_peers.lock().await;

        // 获取当前所有对等点
        let current_peers: HashSet<SocketAddr> = (self.peer_getter)().into_iter().collect();

        // 计算新增和删除的对等点
        let added: Vec<SocketAddr> = current_peers.difference(&known_peers).cloned().collect();
        let dropped: Vec<SocketAddr> = known_peers.difference(&current_peers).cloned().collect();

        // 更新已知对等点
        *known_peers = current_peers;

        // 更新交换时间
        let mut last_exchange = self.last_exchange.lock().await;
        *last_exchange = Instant::now();

        // 构建PEX消息
        let mut added_bytes = Vec::new();
        let mut added_f = Vec::new();
        let mut added6_bytes = Vec::new();
        let mut added6_f = Vec::new();
        let mut dropped_bytes = Vec::new();
        let mut dropped6_bytes = Vec::new();

        // 处理新增的对等点
        for addr in added {
            match addr.ip() {
                IpAddr::V4(ipv4) => {
                    // 添加IPv4地址
                    added_bytes.extend_from_slice(&ipv4.octets());
                    added_bytes.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                    // 添加标志 (默认为可达)
                    added_f.push(PEX_FLAG_REACHABLE);
                },
                IpAddr::V6(ipv6) => {
                    // 添加IPv6地址
                    added6_bytes.extend_from_slice(&ipv6.octets());
                    added6_bytes.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                    // 添加标志 (默认为可达)
                    added6_f.push(PEX_FLAG_REACHABLE);
                },
            }
        }

        // 处理删除的对等点
        for addr in dropped {
            match addr.ip() {
                IpAddr::V4(ipv4) => {
                    // 添加IPv4地址
                    dropped_bytes.extend_from_slice(&ipv4.octets());
                    dropped_bytes.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                },
                IpAddr::V6(ipv6) => {
                    // 添加IPv6地址
                    dropped6_bytes.extend_from_slice(&ipv6.octets());
                    dropped6_bytes.extend_from_slice(&(addr.port() as u16).to_be_bytes());
                },
            }
        }

        Ok(PEXMessage {
            added: added_bytes,
            added_f,
            added6: added6_bytes,
            added6_f,
            dropped: dropped_bytes,
            dropped6: dropped6_bytes,
        })
    }

    /// 处理收到的PEX消息
    pub async fn process_message(&self, message: PEXMessage) -> Result<(), BitTorrentError> {
        // 解析添加的IPv4对等点
        let added_peers = self.parse_added_peers(&message.added, &message.added_f)?;

        // 解析添加的IPv6对等点
        let added_peers6 = self.parse_added_peers6(&message.added6, &message.added6_f)?;

        // 解析删除的对等点
        let dropped_peers = self.parse_dropped_peers(&message.dropped)?;
        let dropped_peers6 = self.parse_dropped_peers6(&message.dropped6)?;

        // 添加新对等点
        for peer in added_peers.iter().chain(added_peers6.iter()) {
            if let Err(e) = (self.peer_adder)(*peer) {
                warn!("Failed to add peer {}: {}", peer, e);
            }
        }

        debug!("PEX: Added {} peers, dropped {} peers",
               added_peers.len() + added_peers6.len(),
               dropped_peers.len() + dropped_peers6.len());

        Ok(())
    }

    // 辅助方法，解析各种对等点数据
    fn parse_added_peers(&self, data: &[u8], flags: &[u8]) -> Result<Vec<SocketAddr>, BitTorrentError> {
        let mut peers = Vec::new();

        // 每个IPv4对等点占6字节
        if data.len() % 6 != 0 {
            return Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other("Invalid PEX added peers data length".to_string()));
        }

        // 检查标志数量是否匹配
        if flags.len() != data.len() / 6 {
            warn!("PEX added peers flags count mismatch");
        }

        for i in 0..(data.len() / 6) {
            let offset = i * 6;

            // 解析IP地址
            let ip = Ipv4Addr::new(
                data[offset],
                data[offset + 1],
                data[offset + 2],
                data[offset + 3],
            );

            // 解析端口
            let port = u16::from_be_bytes([data[offset + 4], data[offset + 5]]);

            // 创建套接字地址
            let addr = SocketAddr::new(IpAddr::V4(ip), port);

            peers.push(addr);
        }

        Ok(peers)
    }

    fn parse_added_peers6(&self, data: &[u8], flags: &[u8]) -> Result<Vec<SocketAddr>, BitTorrentError> {
        let mut peers = Vec::new();

        // 每个IPv6对等点占18字节
        if data.len() % 18 != 0 {
            return Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other("Invalid PEX added6 peers data length".to_string()));
        }

        // 检查标志数量是否匹配
        if flags.len() != data.len() / 18 {
            warn!("PEX added6 peers flags count mismatch");
        }

        for i in 0..(data.len() / 18) {
            let offset = i * 18;

            // 解析IP地址
            let mut ip_bytes = [0u8; 16];
            ip_bytes.copy_from_slice(&data[offset..offset + 16]);
            let ip = Ipv6Addr::from(ip_bytes);

            // 解析端口
            let port = u16::from_be_bytes([data[offset + 16], data[offset + 17]]);

            // 创建套接字地址
            let addr = SocketAddr::new(IpAddr::V6(ip), port);

            peers.push(addr);
        }

        Ok(peers)
    }

    fn parse_dropped_peers(&self, data: &[u8]) -> Result<Vec<SocketAddr>, BitTorrentError> {
        let mut peers = Vec::new();

        // 每个IPv4对等点占6字节
        if data.len() % 6 != 0 {
            return Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other("Invalid PEX dropped peers data length".to_string()));
        }

        for i in 0..(data.len() / 6) {
            let offset = i * 6;

            // 解析IP地址
            let ip = Ipv4Addr::new(
                data[offset],
                data[offset + 1],
                data[offset + 2],
                data[offset + 3],
            );

            // 解析端口
            let port = u16::from_be_bytes([data[offset + 4], data[offset + 5]]);

            // 创建套接字地址
            let addr = SocketAddr::new(IpAddr::V4(ip), port);

            peers.push(addr);
        }

        Ok(peers)
    }

    fn parse_dropped_peers6(&self, data: &[u8]) -> Result<Vec<SocketAddr>, BitTorrentError> {
        let mut peers = Vec::new();

        // 每个IPv6对等点占18字节
        if data.len() % 18 != 0 {
            return Err(crate::protocols::bittorrent::utils::error::BitTorrentError::Other("Invalid PEX dropped6 peers data length".to_string()));
        }

        for i in 0..(data.len() / 18) {
            let offset = i * 18;

            // 解析IP地址
            let mut ip_bytes = [0u8; 16];
            ip_bytes.copy_from_slice(&data[offset..offset + 16]);
            let ip = Ipv6Addr::from(ip_bytes);

            // 解析端口
            let port = u16::from_be_bytes([data[offset + 16], data[offset + 17]]);

            // 创建套接字地址
            let addr = SocketAddr::new(IpAddr::V6(ip), port);

            peers.push(addr);
        }

        Ok(peers)
    }
}

#[async_trait]
impl ExtensionHandler for PEXExtension {
    async fn handle_message(&self, _message_id: u8, payload: &[u8]) -> Result<(), BitTorrentError> {
        // 解码PEX消息
        let message = PEXMessage::decode(payload)?;

        // 处理PEX消息
        self.process_message(message).await
    }
    
    /// 处理扩展消息（带info_hash）
    async fn handle_message_with_info_hash(&self, _message_id: u8, payload: &[u8], info_hash: &[u8]) -> Result<(), BitTorrentError> {
        // 解码PEX消息
        let message = PEXMessage::decode(payload)?;

        // 处理PEX消息并关联info_hash
        self.process_message_with_info_hash(message, info_hash).await
    }

    fn extension_id(&self) -> &str {
        "ut_pex"
    }

    fn message_id(&self) -> u8 {
        self.message_id
    }

    fn set_message_id(&mut self, id: u8) {
        self.message_id = id;
    }

    async fn generate_message(&self) -> Result<Vec<u8>, BitTorrentError> {
        self.generate_pex_message().await?.encode()
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

/// 处理收到的PEX消息并关联info_hash
impl PEXExtension {
    pub async fn process_message_with_info_hash(&self, message: PEXMessage, info_hash: &[u8]) -> Result<(), BitTorrentError> {
        // 解析添加的IPv4对等点
        let added_peers = self.parse_added_peers(&message.added, &message.added_f)?;

        // 解析添加的IPv6对等点
        let added_peers6 = self.parse_added_peers6(&message.added6, &message.added6_f)?;

        // 解析删除的对等点
        let dropped_peers = self.parse_dropped_peers(&message.dropped)?;
        let dropped_peers6 = self.parse_dropped_peers6(&message.dropped6)?;

        // 将info_hash转换为固定大小的数组
        let mut info_hash_array = [0u8; 20];
        if info_hash.len() == 20 {
            info_hash_array.copy_from_slice(info_hash);
            
            // 添加新对等点并关联info_hash
            for peer in added_peers.iter().chain(added_peers6.iter()) {
                // 添加对等点与info_hash的关联
                self.add_peer_with_info_hash(*peer, info_hash_array).await;
                
                // 添加对等点到队列
                if let Err(e) = (self.peer_adder)(*peer) {
                    warn!("Failed to add peer {}: {}", peer, e);
                }
            }
            
            debug!("PEX: Added {} peers with info_hash {}, dropped {} peers",
                   added_peers.len() + added_peers6.len(),
                   hex::encode(info_hash),
                   dropped_peers.len() + dropped_peers6.len());
        } else {
            // 如果info_hash长度不正确，则使用原始方法处理
            debug!("Invalid info_hash length: {}, using default process_message", info_hash.len());
            return self.process_message(message).await;
        }

        Ok(())
    }
}
