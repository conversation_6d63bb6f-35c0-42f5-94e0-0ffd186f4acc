use anyhow::{Result, anyhow};
use crate::core::p2p::peer::Peer;
use crate::core::p2p::piece::PieceManager;
use tokio::time::Duration;
use tracing::{debug, info, warn, error};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::collections::HashMap;
use tokio::sync::broadcast;
use crate::protocols::bittorrent::piece_manager::PieceEvent;
use crate::protocols::bittorrent::piece_manager::BitTorrentPieceManager;

use crate::core::p2p::protocol::Protocol;
use crate::core::p2p::protocol::DownloadStatus as P2PDownloadStatus;
use crate::protocols::bittorrent::piece_factory::create_piece_manager_with_strategy;
use crate::protocols::bittorrent::piece_manager::PieceSelectionStrategy;
use crate::core::interfaces::downloader::DownloadOptions;
use crate::core::interfaces::DownloadStatus as InterfaceDownloadStatus;
use crate::protocols::bittorrent::block_manager::{<PERSON><PERSON><PERSON>ger<PERSON>allback, Piece<PERSON><PERSON>ple<PERSON><PERSON><PERSON><PERSON>, BlockAdded<PERSON>allback};
use crate::protocols::bittorrent::stats_manager::StatsManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use crate::protocols::bittorrent::tracker_manager::TrackerResponse;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::core::p2p::protocol::DownloadStats;

use super::core::BitTorrentProtocol;

/// BitTorrentPieceManagerWrapper 结构体
/// 用于将 PieceManager 包装为 BlockManagerCallback
/// 这是一个更规范的实现方式，避免了在函数内部定义适配器结构体
pub struct BitTorrentPieceManagerWrapper {
    piece_manager: Arc<Mutex<dyn PieceManager>>
}

#[async_trait::async_trait]
impl PieceCompleteCallback for BitTorrentPieceManagerWrapper {
    async fn on_piece_complete(&mut self, piece_index: u32, piece_data: Vec<u8>) -> Result<bool> {
        let mut manager = self.piece_manager.lock().await;
        manager.add_piece_data(piece_index, piece_data).await.map_err(|e| anyhow::anyhow!("{}", e))
    }
}

#[async_trait::async_trait]
impl BlockAddedCallback for BitTorrentPieceManagerWrapper {
    async fn on_block_added(&self, piece_index: u32, progress: f64) -> Result<()> {
        // 记录进度信息
        debug!("Block added for piece {}, progress: {:.2}%", piece_index, progress * 100.0);
        Ok(())
    }
}

// 实现标记性trait
impl BlockManagerCallback for BitTorrentPieceManagerWrapper {}

// 将interfaces::downloader::DownloadStatus转换为p2p::protocol::DownloadStatus
fn convert_status(status: InterfaceDownloadStatus) -> P2PDownloadStatus {
    match status {
        InterfaceDownloadStatus::Pending => P2PDownloadStatus::Pending,
        InterfaceDownloadStatus::Initializing => P2PDownloadStatus::Initialized,
        InterfaceDownloadStatus::Downloading => P2PDownloadStatus::Downloading,
        InterfaceDownloadStatus::Paused => P2PDownloadStatus::Paused,
        InterfaceDownloadStatus::Completed => P2PDownloadStatus::Completed,
        InterfaceDownloadStatus::Failed => P2PDownloadStatus::Failed,
        InterfaceDownloadStatus::Cancelled => P2PDownloadStatus::Cancelled,
    }
}

// 将p2p::protocol::DownloadStatus转换为interfaces::downloader::DownloadStatus
fn convert_status_to_interface(status: P2PDownloadStatus) -> InterfaceDownloadStatus {
    match status {
        P2PDownloadStatus::Pending => InterfaceDownloadStatus::Pending,
        P2PDownloadStatus::Initialized => InterfaceDownloadStatus::Initializing,
        P2PDownloadStatus::Downloading => InterfaceDownloadStatus::Downloading,
        P2PDownloadStatus::Paused => InterfaceDownloadStatus::Paused,
        P2PDownloadStatus::Completed => InterfaceDownloadStatus::Completed,
        P2PDownloadStatus::Failed => InterfaceDownloadStatus::Failed,
        P2PDownloadStatus::Cancelled => InterfaceDownloadStatus::Cancelled,
    }
}

// 移除本文件内的 pub struct BitTorrentProtocol、pub trait StatsManagerTrait、pub trait PeerManagerTrait、pub trait BlockManagerTrait、pub trait PieceManagerTrait 的重复定义，仅保留 use crate::protocols::bittorrent::manager::peer_manager_trait::PeerManagerTrait; use crate::protocols::bittorrent::block_manager_trait::BlockManagerTrait; use crate::protocols::bittorrent::piece_manager_trait::PieceManagerTrait; use crate::protocols::bittorrent::stats_manager::StatsManagerTrait; 等路径。

#[async_trait::async_trait]
impl Protocol for BitTorrentProtocol {
    fn protocol_type(&self) -> crate::core::p2p::protocol::ProtocolType {
        crate::core::p2p::protocol::ProtocolType::BitTorrent
    }
    
    async fn init(&mut self) -> Result<()> {
        // 已在其他地方实现
        Ok(())
    }
    
    async fn download(&mut self) -> Result<()> {
        // 已在其他地方实现
        Ok(())
    }
    
    async fn pause(&mut self) -> Result<()> {
        // 已在其他地方实现
        Ok(())
    }
    
    async fn resume(&mut self) -> Result<()> {
        // 已在其他地方实现
        Ok(())
    }
    
    async fn cancel(&mut self) -> Result<()> {
        // 已在其他地方实现
        Ok(())
    }
    
    async fn progress(&self) -> anyhow::Result<f64> {
        Ok(self.stats_manager.progress())
    }
    
    async fn speed(&self) -> anyhow::Result<u64> {
        Ok(self.stats_manager.download_speed())
    }
    
    async fn downloaded_size(&self) -> Result<u64> {
        Ok(self.downloaded)
    }
    
    async fn total_size(&self) -> Result<Option<u64>> {
        if let Some(torrent_info) = &self.torrent_info {
            Ok(Some(torrent_info.total_size))
        } else {
            Ok(None)
        }
    }
    
    fn status(&self) -> P2PDownloadStatus {
        convert_status(self.status)
    }
    
    async fn stats(&self) -> crate::core::p2p::protocol::DownloadStats {
        self.stats_manager.get_stats()
    }
    
    fn clone_box(&self) -> Box<dyn Protocol> {
        Box::new(self.clone())
    }
}

impl BitTorrentProtocol {
    /// BitTorrent 主下载循环（事件驱动）
    pub(crate) async fn download_loop(&mut self, url: &str, output_path: &str, options: &DownloadOptions) -> Result<(), BitTorrentError> {
        debug!("Starting BitTorrent download loop for task {} with URL {} and output path {}", self.task_id, url, output_path);

        // 检查是否已初始化
        if !self.initialized {
            return Err(anyhow!("BitTorrent protocol not initialized").into());
        }

        // 检查是否有种子信息
        let _torrent_info = self.torrent_info.as_ref()
            .ok_or_else(|| anyhow!("No torrent information available"))?;

        // 应用下载选项
        if let Some(speed_limit) = options.speed_limit {
            debug!("Setting download speed limit to {} bytes/s for task {}", speed_limit, self.task_id);
            self.set_download_limit(Some(speed_limit));
        }

        // 应用连接数限制
        if let Some(max_connections) = options.connections {
            if let Some(peer_manager) = &mut self.peer_manager {
                debug!("Setting max connections to {} for task {}", max_connections, self.task_id);
                peer_manager.set_max_peers(max_connections as usize);
            }
        }

        // 创建分片管理器（如果尚未创建）
        if self.piece_manager.is_none() {
            if let Some(torrent_info) = &self.torrent_info {
                // 获取分片选择策略
                let piece_selection_strategy_str = self.settings.piece_selection_strategy.clone().unwrap_or_else(|| "rarest_first".to_string());
                let initial_strategy = match piece_selection_strategy_str.as_str() {
                    "sequential" => PieceSelectionStrategy::Sequential,
                    "rarest_first" => PieceSelectionStrategy::RarestFirst,
                    "random_first" => PieceSelectionStrategy::RandomFirst,
                    "end_game" => PieceSelectionStrategy::EndGame,
                    _ => PieceSelectionStrategy::RarestFirst, // 默认值
                };
                
                // 使用传入的output_path参数而不是self.output_path
                match create_piece_manager_with_strategy(torrent_info.clone(), output_path, initial_strategy).await {
                    Ok(piece_manager) => {
                        self.piece_manager = Some(piece_manager.clone());
                        // 设置BlockManager的回调
                        let callback: Arc<dyn BlockAddedCallback> = Arc::new(BitTorrentPieceManagerWrapper {
                            piece_manager: piece_manager.clone()
                        });
                        self.block_manager.set_block_added_callback(callback);
                        
                        info!("Piece manager created for task {} with strategy {:?}", self.task_id, initial_strategy);
                    },
                    Err(e) => {
                        error!("Failed to create piece manager: {}", e);
                        return Err(anyhow!("Failed to create piece manager: {}", e).into());
                    }
                }
            } else {
                return Err(anyhow!("No torrent information available").into());
            }
        }

        // 获取分片事件通道
        let mut piece_event_rx = if let Some(piece_manager) = &self.piece_manager {
            if let Ok(locked) = piece_manager.try_lock() {
                if let Some(real_pm) = locked.as_any().downcast_ref::<BitTorrentPieceManager>() {
                    real_pm.event_sender.as_ref().map(|s| s.subscribe())
                } else {
                    None
                }
            } else {
                None
            }
        } else { None };
        let mut interval = tokio::time::interval(Duration::from_secs(1));
        loop {
            tokio::select! {
                _ = interval.tick() => {
                    // 更新统计信息
                    self.update_stats().await?;

                    // 处理对等点连接和消息
                    self.process_peers().await?;

                    // 处理上传任务
                    if self.upload_enabled {
                        // 使用enable_upload方法的状态
                        self.process_uploads().await?;
                        
                        // 记录已上传的数据大小
                        let current_uploaded = self.uploaded_size();
                        debug!("Current uploaded size: {} bytes", current_uploaded);
                    }

                    // 检查超时的块请求
                    self.block_manager.lock().await.check_timeouts().await;

                    // 定期向Tracker发送请求
                    if let Some(tracker_manager) = &mut self.tracker_manager {
                        if tracker_manager.should_update() {
                            // 使用本地变量保存tracker_manager的引用
                            let active_count = tracker_manager.active_tracker_count();
                            let total_count = tracker_manager.total_tracker_count();

                            // 使用announce_to_tracker方法，它会尝试所有可用的Tracker
                            match self.announce_to_tracker("none").await {
                                Ok(response) => {
                                    // 连接新的对等点
                                    if let Some(peer_manager) = &mut self.peer_manager {
                                        if let Some(torrent_info) = &self.torrent_info {
                                            peer_manager.lock().await.add_peers_from_tracker(response, torrent_info).await?;
                                        }
                                    }

                                    // 打印Tracker统计信息
                                    debug!("Connected to {} active trackers out of {} total trackers",
                                        active_count, total_count);
                                },
                                Err(e) => {
                                    warn!("Failed to announce to tracker: {}", e);
                                    // 继续执行，因为我们可以通过DHT获取对等点
                                }
                            }
                        }
                    }

                    // 获取当前下载统计信息
                    let current_stats = self.stats().await;
                    debug!("Current download stats: progress={:.2}%, speed={} bytes/s, peers={}/{}", 
                        current_stats.progress * 100.0, 
                        current_stats.download_speed, 
                        current_stats.connected_peers, 
                        current_stats.total_peers);

                    // 检查下载是否完成
                    if let Some(piece_manager) = &self.piece_manager {
                        if piece_manager.lock().await.is_complete().await? {
                            self.status = InterfaceDownloadStatus::Completed;
                            break;
                        }
                    }
                }
                Some(mut rx) = async { piece_event_rx.as_mut() } , if piece_event_rx.is_some() => {
                    if let Ok(event) = rx.recv().await {
                        match event {
                            PieceEvent::PieceCompleted { index } => {
                                debug!("Piece {} completed, can trigger UI/后续逻辑", index);
                                // 可在此处自动切换状态、通知UI、触发后续逻辑等
                            }
                            _ => {}
                        }
                    }
                }
            }
            // 检查下载是否完成
            if self.status() == InterfaceDownloadStatus::Completed {
                break;
            }
        }

        // 如果下载完成，向Tracker发送completed事件
        if self.status() == InterfaceDownloadStatus::Completed {
            // 先获取所有需要的值，避免同时借用self
            let uploaded_size = self.uploaded_size();
            let downloaded_size = self.downloaded;
            
            if let Some(tracker_manager) = &mut self.tracker_manager {
                if let Some(torrent_info) = &self.torrent_info {
                    if let Err(e) = tracker_manager.announce(
                        torrent_info,
                        "completed",
                        uploaded_size,
                        downloaded_size,
                        0
                    ).await {
                        warn!("Failed to send completed event to tracker: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 检查超时的块请求
    pub(crate) async fn check_block_timeouts(&mut self) -> Result<(), BitTorrentError> {
        let reset_pieces = self.block_manager.lock().await.check_timeouts().await;
        if !reset_pieces.is_empty() {
            debug!("Reset {} timed out pieces", reset_pieces.len());
        }
        Ok(())
    }

    /// 检查下载是否完成
    pub(crate) async fn check_download_complete(&mut self) -> Result<bool, BitTorrentError> {
        if let Some(piece_manager) = &self.piece_manager {
            return piece_manager.lock().await.is_complete().await;
        }
        Ok(false)
    }

    /// 设置下载速率限制
    pub async fn set_download_limit(&mut self, bytes_per_second: Option<u64>) -> Result<()> {
        // 如果有带宽调度器，设置下载速率限制
        if let Some(scheduler) = &self.bandwidth_scheduler {
            scheduler.set_task_download_limit(self.task_id, bytes_per_second).await?;
        }

        // 如果有对等点管理器，更新所有对等点的下载速率限制
        if let Some(peer_manager) = &mut self.peer_manager {
            let peers = peer_manager.get_peers();
            let peers_count = peers.len() as u64;

            // 计算每个对等点的下载速率限制
            let per_peer_limit = if let Some(limit) = bytes_per_second {
                if peers_count > 0 {
                    Some(limit / peers_count)
                } else {
                    Some(limit)
                }
            } else {
                None
            };

            for (_, peer_arc) in peers {
                let mut peer = peer_arc.lock().await;
                if peer.is_connected() {
                    // 设置对等点的下载速率限制
                    // 注意：这里需要确保BitTorrentPeer有设置下载速率限制的方法
                    // 如果没有，可能需要在BitTorrentPeer中添加相应的方法
                    if let Some(limit) = per_peer_limit {
                        peer.connection.common.set_download_rate_limit(limit);
                    } else {
                        peer.connection.common.set_download_rate_limit(0); // 0表示无限制
                    }
                }
            }
        }

        info!("Download rate limit set to {:?} bytes/second for task {}", bytes_per_second, self.task_id);
        Ok(())
    }
}

