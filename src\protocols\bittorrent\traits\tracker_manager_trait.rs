use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::protocols::bittorrent::utils::error::BitTorrentError;
use crate::protocols::bittorrent::tracker::TrackerResponse;
use async_trait::async_trait;

#[async_trait]
pub trait TrackerManagerTrait: Send + Sync {
    async fn announce(
        &mut self,
        torrent_info: &TorrentInfo,
        event: &str,
        uploaded: u64,
        downloaded: u64,
        left: u64
    ) -> Result<TrackerResponse, BitTorrentError>;
    async fn init_trackers(&mut self, torrent_info: &TorrentInfo) -> Result<(), BitTorrentError>;
    fn should_update(&self) -> bool;
    fn active_tracker_count(&self) -> usize;
    fn total_tracker_count(&self) -> usize;
    // ...可扩展其它接口...
}
