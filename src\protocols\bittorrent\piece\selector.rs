use std::collections::HashSet;
use std::net::SocketAddr;
use std::time::Duration;
use tracing::{info, debug};

use crate::core::p2p::piece::PieceState;
use super::peer_scoring::{PeerScore, PeerScoreManager};
use super::selection_strategies::{PieceSelectionStrategy, SelectionStrategy, create_strategy};
use super::peer_availability::PeerAvailabilityManager;

/// 分片选择器配置
#[derive(Debug, Clone)]
pub struct PieceSelectorConfig {
    /// 初始选择策略
    pub initial_strategy: PieceSelectionStrategy,
    /// 端游模式阈值（剩余分片数量）
    pub end_game_threshold: usize,
    /// 随机首块数量
    pub random_first_pieces: usize,
    /// 总分片数量
    pub total_pieces: usize,
    /// 是否考虑对等点性能
    pub consider_peer_performance: bool,
}

impl Default for PieceSelectorConfig {
    fn default() -> Self {
        Self {
            initial_strategy: PieceSelectionStrategy::RarestFirst,
            end_game_threshold: 50,
            random_first_pieces: 5,
            total_pieces: 1000,
            consider_peer_performance: true,
        }
    }
}

/// 分片选择器
/// 
/// 负责协调各个子模块，实现分片选择的核心逻辑。
/// 采用依赖注入的方式，将具体的功能委托给专门的管理器。
pub struct PieceSelector {
    /// 配置
    config: PieceSelectorConfig,
    /// 当前选择策略
    current_strategy: PieceSelectionStrategy,
    /// 策略实例
    strategy_instance: Box<dyn SelectionStrategy>,
    /// 已验证的分片数量
    verified_pieces: usize,
    /// 对等点评分管理器
    peer_score_manager: PeerScoreManager,
    /// 对等点可用性管理器
    peer_availability_manager: PeerAvailabilityManager,
}

impl PieceSelector {
    /// 创建新的分片选择器
    pub fn new(config: PieceSelectorConfig) -> Self {
        let strategy_instance = create_strategy(config.initial_strategy);
        let peer_score_manager = PeerScoreManager::new(config.consider_peer_performance);
        let peer_availability_manager = PeerAvailabilityManager::new();

        Self {
            current_strategy: config.initial_strategy,
            strategy_instance,
            config,
            verified_pieces: 0,
            peer_score_manager,
            peer_availability_manager,
        }
    }

    /// 使用默认配置创建分片选择器
    pub fn with_defaults() -> Self {
        Self::new(PieceSelectorConfig::default())
    }

    /// 更新已验证的分片数量
    pub fn update_verified_pieces(&mut self, verified_pieces: usize) {
        self.verified_pieces = verified_pieces;
        self.check_strategy_switch();
    }

    /// 检查是否应该切换策略
    fn check_strategy_switch(&mut self) {
        let old_strategy = self.current_strategy;
        
        // 如果使用随机首块策略，检查是否已下载足够的随机首块
        if self.current_strategy == PieceSelectionStrategy::RandomFirst &&
           self.verified_pieces >= self.config.random_first_pieces {
            self.current_strategy = PieceSelectionStrategy::RarestFirst;
            info!("Switching to rarest first strategy after downloading {} random pieces", self.verified_pieces);
        }

        // 检查是否应该进入端游模式
        let missing_pieces = self.config.total_pieces - self.verified_pieces;
        if missing_pieces <= self.config.end_game_threshold &&
           self.current_strategy != PieceSelectionStrategy::EndGame {
            self.current_strategy = PieceSelectionStrategy::EndGame;
            info!("Entering end game mode with {} pieces remaining", missing_pieces);
        }

        // 如果策略发生变化，更新策略实例
        if old_strategy != self.current_strategy {
            self.strategy_instance = create_strategy(self.current_strategy);
        }
    }

    /// 获取当前策略
    pub fn get_strategy(&self) -> PieceSelectionStrategy {
        self.current_strategy
    }

    /// 设置策略
    pub fn set_strategy(&mut self, strategy: PieceSelectionStrategy) {
        if self.current_strategy != strategy {
            self.current_strategy = strategy;
            self.strategy_instance = create_strategy(strategy);
        }
    }

    /// 设置总分片数量
    pub fn set_total_pieces(&mut self, total_pieces: u32) {
        self.config.total_pieces = total_pieces as usize;
        self.check_strategy_switch();
    }

    /// 选择下一个要下载的分片
    pub fn select_next_piece(
        &self,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        let piece_rarities = self.peer_availability_manager.get_piece_rarity_array(piece_states.len());
        
        self.strategy_instance.select_piece(
            peer_pieces,
            piece_states,
            requested_pieces,
            &piece_rarities
        )
    }

    /// 为特定对等点选择下一个要下载的分片
    pub fn select_next_piece_for_peer(
        &self,
        peer_addr: &SocketAddr,
        peer_pieces: &HashSet<u32>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Option<u32> {
        let piece_rarities = self.peer_availability_manager.get_piece_rarity_array(piece_states.len());
        let peer_score = self.peer_score_manager.get_peer_score(peer_addr);
        
        self.strategy_instance.select_piece_for_peer(
            peer_addr,
            peer_score.as_ref(),
            peer_pieces,
            piece_states,
            requested_pieces,
            &piece_rarities
        )
    }

    /// 更新对等点评分
    pub fn update_peer_score(&mut self, peer_addr: SocketAddr, download_speed: u64, success: bool, response_time: u64) {
        self.peer_score_manager.update_peer_score(peer_addr, download_speed, success, response_time);
    }

    /// 获取对等点评分
    pub fn get_peer_score(&self, peer_addr: &SocketAddr) -> Option<PeerScore> {
        self.peer_score_manager.get_peer_score(peer_addr)
    }

    /// 更新对等点拥有的分片
    pub fn update_peer_pieces(&mut self, peer_addr: &SocketAddr, peer_pieces: &HashSet<u32>) {
        self.peer_availability_manager.update_peer_pieces(peer_addr, peer_pieces);
    }

    /// 添加对等点拥有的分片
    pub fn add_peer_piece(&mut self, peer_addr: &SocketAddr, piece_index: u32) {
        self.peer_availability_manager.add_peer_piece(peer_addr, piece_index);
    }

    /// 移除对等点
    pub fn remove_peer(&mut self, peer_addr: &SocketAddr) {
        self.peer_availability_manager.remove_peer(peer_addr);
    }

    /// 清理过期的对等点评分
    pub fn cleanup_expired_peer_scores(&mut self, max_age: Duration) {
        self.peer_score_manager.cleanup_expired_scores(max_age);
    }

    /// 设置是否考虑对等点性能
    pub fn set_consider_peer_performance(&mut self, consider: bool) {
        self.peer_score_manager.set_consider_peer_performance(consider);
        self.config.consider_peer_performance = consider;
    }

    /// 获取是否考虑对等点性能
    pub fn is_considering_peer_performance(&self) -> bool {
        self.peer_score_manager.is_considering_peer_performance()
    }

    /// 获取分片稀有度
    pub fn get_piece_rarity(&self, piece_index: u32) -> usize {
        self.peer_availability_manager.get_piece_rarity(piece_index)
    }

    /// 获取拥有指定分片的对等点列表
    pub fn get_peers_with_piece(&self, piece_index: u32) -> Vec<SocketAddr> {
        self.peer_availability_manager.get_peers_with_piece(piece_index)
    }

    /// 检查对等点是否拥有指定分片
    pub fn peer_has_piece(&self, peer_addr: &SocketAddr, piece_index: u32) -> bool {
        self.peer_availability_manager.peer_has_piece(peer_addr, piece_index)
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> PieceSelectorStats {
        let (peer_count, known_piece_count, total_piece_instances) = self.peer_availability_manager.get_stats();
        let peer_score_count = self.peer_score_manager.peer_count();
        
        PieceSelectorStats {
            current_strategy: self.current_strategy,
            verified_pieces: self.verified_pieces,
            total_pieces: self.config.total_pieces,
            peer_count,
            peer_score_count,
            known_piece_count,
            total_piece_instances,
        }
    }

    /// 获取配置
    pub fn get_config(&self) -> &PieceSelectorConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: PieceSelectorConfig) {
        let old_strategy = self.config.initial_strategy;
        self.config = config;
        
        // 如果策略发生变化，更新策略实例
        if old_strategy != self.config.initial_strategy {
            self.current_strategy = self.config.initial_strategy;
            self.strategy_instance = create_strategy(self.current_strategy);
        }
        
        // 更新子管理器的配置
        self.peer_score_manager.set_consider_peer_performance(self.config.consider_peer_performance);
        
        // 重新检查策略切换
        self.check_strategy_switch();
    }
}

/// 分片选择器统计信息
#[derive(Debug, Clone)]
pub struct PieceSelectorStats {
    /// 当前策略
    pub current_strategy: PieceSelectionStrategy,
    /// 已验证分片数量
    pub verified_pieces: usize,
    /// 总分片数量
    pub total_pieces: usize,
    /// 对等点数量
    pub peer_count: usize,
    /// 有评分的对等点数量
    pub peer_score_count: usize,
    /// 已知分片数量
    pub known_piece_count: usize,
    /// 总分片实例数量
    pub total_piece_instances: usize,
}

impl Default for PieceSelector {
    fn default() -> Self {
        Self::with_defaults()
    }
}
