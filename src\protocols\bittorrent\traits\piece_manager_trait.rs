use crate::protocols::bittorrent::utils::error::BitTorrentError;
use std::any::Any;

#[async_trait::async_trait]
pub trait PieceManagerTrait: Any + Send + Sync {
    fn as_any(&self) -> &dyn Any;
    async fn is_complete(&self) -> Result<bool,BitTorrentError>;
    async fn progress(&self) -> Result<f64,BitTorrentError>;
    async fn downloaded_size(&self) -> Result<u64,BitTorrentError>;
    // ...可扩展其它接口...
}
