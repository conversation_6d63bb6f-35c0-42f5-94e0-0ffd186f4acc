use async_trait::async_trait;
use crate::core::p2p::protocol::DownloadStats;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// 统计管理器异步trait，定义统计相关异步操作接口
#[async_trait]
pub trait StatsManagerTrait: Send + Sync {
    /// 获取下载进度
    async fn progress(&self) -> f64;
    /// 获取下载速度
    async fn download_speed(&self) -> u64;
    /// 获取统计信息
    async fn get_stats(&self) -> DownloadStats;
    // ...可扩展其它统计相关接口...
}
