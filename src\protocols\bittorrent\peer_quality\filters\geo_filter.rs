//! 地理位置过滤器实现

use std::net::SocketAddr;
use std::sync::Arc;
use std::pin::Pin;
use std::future::Future;
use anyhow::Result;

use crate::protocols::bittorrent::peer_quality::models::EnhancedPeerScore;
use crate::protocols::bittorrent::peer_quality::filters::{PeerFilterTrait, GeoLocationServiceTrait};

/// 地理位置过滤器
pub struct GeoFilter {
    /// 地理位置服务
    geo_service: Option<Arc<dyn GeoLocationServiceTrait>>,
    /// 允许的国家/地区代码
    allowed_countries: Vec<String>,
    /// 过滤器名称
    name: String,
    /// 过滤器描述
    description: String,
}

impl GeoFilter {
    /// 创建新的地理位置过滤器
    pub fn new(geo_service: Option<Arc<dyn GeoLocationServiceTrait>>, allowed_countries: Vec<String>) -> Self {
        Self {
            geo_service,
            allowed_countries,
            name: "GeoFilter".to_string(),
            description: "基于地理位置过滤对等点".to_string(),
        }
    }
    
    /// 设置地理位置服务
    pub fn set_geo_service(&mut self, geo_service: Option<Arc<dyn GeoLocationServiceTrait>>) {
        self.geo_service = geo_service;
    }
}

impl PeerFilterTrait for GeoFilter {
    fn filter(&self, addr: &SocketAddr, _score: Option<&EnhancedPeerScore>) -> Pin<Box<dyn Future<Output = Result<bool>> + Send + '_>> {
        let addr = *addr;
        let geo_service = self.geo_service.clone();
        let allowed_countries = self.allowed_countries.clone();
        Box::pin(async move {
            // 如果没有地理位置服务或允许的国家/地区列表为空，默认通过
            if geo_service.is_none() || allowed_countries.is_empty() {
                return Ok(true);
            }
            
            // 获取对等点的国家/地区代码
            if let Some(geo_service) = &geo_service {
                if let Some(country_code) = geo_service.get_country_code(&addr) {
                    // 检查国家/地区代码是否在允许列表中
                    return Ok(allowed_countries.contains(&country_code));
                }
            }
            
            // 如果无法获取地理位置信息，默认通过
            Ok(true)
        })
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
}