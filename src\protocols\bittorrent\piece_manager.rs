use async_trait::async_trait;
use std::collections::HashSet;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, info, warn, error};
use tokio::sync::{broadcast, RwLock};
use anyhow::Error;

use crate::core::p2p::piece::PIECE_CACHE;
use crate::core::p2p::piece::{PieceManager, PieceState, PieceInfo};
use super::torrent::TorrentInfo;
use super::file_manager::FileManager;
use super::piece::{
    PieceSelector, PieceSelectorFactory, PieceSelectorConfig, PieceSelectionStrategy,
    Piece, Block, BlockState, PieceVerifier, DownloadQueueManager,
    PieceEventManager, PieceEvent, PieceStorageManager, PieceStorageConfig,
    ConsoleEventListener
};
use super::block_manager::{BlockManagerCallback, PieceCompleteCallback, BlockAddedCallback};
use crate::protocols::bittorrent::traits::PieceManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

// 所有数据结构和功能现在都在 piece 模块中

/// BitTorrent分片管理器配置
#[derive(Debug, Clone)]
pub struct PieceManagerConfig {
    /// 块大小
    pub block_size: u32,
    /// 请求超时时间
    pub request_timeout: Duration,
    /// 最大重试次数
    pub max_retry_count: u32,
    /// 每个对等点的最大并发请求数
    pub max_requests_per_peer: usize,
    /// 是否启用快速验证模式
    pub fast_verification: bool,
    /// 是否启用写入缓存
    pub enable_write_cache: bool,
    /// 写入缓存大小
    pub write_cache_size: usize,
}

impl Default for PieceManagerConfig {
    fn default() -> Self {
        Self {
            block_size: 16384, // 16KB
            request_timeout: Duration::from_secs(30),
            max_retry_count: 3,
            max_requests_per_peer: 5,
            fast_verification: false,
            enable_write_cache: true,
            write_cache_size: 16 * 1024 * 1024, // 16MB
        }
    }
}

/// BitTorrent分片管理器
///
/// 重构后的分片管理器，使用模块化架构，将具体功能委托给专门的管理器
pub struct BitTorrentPieceManager {
    /// 配置
    config: PieceManagerConfig,
    /// 分片列表
    pieces: Vec<Piece>,
    /// 分片状态
    piece_states: Vec<PieceState>,
    /// 已下载的分片数量
    pieces_downloaded: usize,
    /// 已验证的分片数量
    pieces_verified: usize,
    /// 总分片数量
    pieces_total: usize,
    /// 已下载的数据大小
    downloaded_size: u64,
    /// 总数据大小
    total_size: u64,
    /// 下载进度
    progress: f64,
    /// 已初始化
    initialized: bool,
    /// 已请求的分片集合
    requested_pieces: HashSet<u32>,

    // 模块化组件
    /// 文件管理器
    file_manager: Arc<RwLock<FileManager>>,
    /// 分片选择器
    piece_selector: PieceSelector,
    /// 分片验证器
    piece_verifier: PieceVerifier,
    /// 下载队列管理器
    download_queue_manager: DownloadQueueManager,
    /// 事件管理器
    event_manager: PieceEventManager,
    /// 存储管理器
    storage_manager: PieceStorageManager,

    /// 事件通道（向后兼容性）
    event_sender: Option<broadcast::Sender<PieceEvent>>,
}

impl BitTorrentPieceManager {
    /// 创建新的BitTorrent分片管理器，使用默认配置和RandomFirst策略
    pub async fn new(torrent_info: TorrentInfo, output_path: impl AsRef<Path>) -> Result<Self,BitTorrentError> {
        Self::new_with_config(torrent_info, output_path, PieceManagerConfig::default(), PieceSelectionStrategy::RandomFirst).await
    }

    /// 创建新的BitTorrent分片管理器，使用指定的分片选择策略
    pub async fn new_with_strategy(
        torrent_info: TorrentInfo,
        output_path: impl AsRef<Path>,
        initial_strategy: PieceSelectionStrategy
    ) -> Result<Self,BitTorrentError> {
        Self::new_with_config(torrent_info, output_path, PieceManagerConfig::default(), initial_strategy).await
    }

    /// 创建新的BitTorrent分片管理器，使用完整配置
    pub async fn new_with_config(
        torrent_info: TorrentInfo,
        output_path: impl AsRef<Path>,
        config: PieceManagerConfig,
        initial_strategy: PieceSelectionStrategy
    ) -> Result<Self,BitTorrentError> {
        let total_size = torrent_info.total_size;
        let piece_length = torrent_info.piece_length;
        let pieces_total = ((total_size + piece_length - 1) / piece_length) as usize;

        // 创建分片列表
        let mut pieces = Vec::with_capacity(pieces_total);
        let mut piece_states = Vec::with_capacity(pieces_total);

        // 检查pieces哈希列表长度
        if torrent_info.pieces.len() != pieces_total * 20 && !torrent_info.pieces.is_empty() {
            warn!("Pieces hash list length mismatch: expected {}, got {}", pieces_total * 20, torrent_info.pieces.len());
        }

        // 创建分片
        for i in 0..pieces_total {
            let piece_size = if i == pieces_total - 1 && total_size % piece_length != 0 {
                total_size % piece_length
            } else {
                piece_length
            };

            // 获取分片哈希
            let hash = if !torrent_info.pieces.is_empty() && i < torrent_info.pieces.len() {
                torrent_info.pieces[i].clone()
            } else {
                // 对于磁力链接，可能没有哈希信息
                vec![0; 20]
            };

            pieces.push(Piece::new(i as u32, piece_size, hash, config.block_size));
            piece_states.push(PieceState::Missing);
        }

        // 创建文件管理器
        let file_manager = Arc::new(RwLock::new(
            FileManager::new(torrent_info.clone(), output_path).await?
        ));

        // 创建分片选择器
        let selector_config = PieceSelectorConfig {
            initial_strategy,
            end_game_threshold: (pieces_total / 20).max(4), // 5% 或至少4个分片
            random_first_pieces: (pieces_total / 50).max(3).min(10), // 2% 或3-10个分片
            total_pieces: pieces_total,
            consider_peer_performance: true,
        };
        let piece_selector = PieceSelector::new(selector_config);

        // 创建分片验证器
        let piece_verifier = PieceVerifier::new(config.fast_verification, config.max_retry_count);

        // 创建下载队列管理器
        let download_queue_manager = DownloadQueueManager::new(
            config.request_timeout,
            config.max_retry_count,
            config.max_requests_per_peer,
        );

        // 创建事件管理器
        let (event_manager, _event_receiver) = PieceEventManager::new(true);

        // 创建存储管理器
        let storage_config = PieceStorageConfig {
            enable_write_cache: config.enable_write_cache,
            write_cache_size: config.write_cache_size,
            ..Default::default()
        };
        let storage_manager = PieceStorageManager::new(storage_config, file_manager.clone());

        Ok(Self {
            config,
            pieces,
            piece_states,
            pieces_downloaded: 0,
            pieces_verified: 0,
            pieces_total,
            downloaded_size: 0,
            total_size,
            progress: 0.0,
            initialized: false,
            requested_pieces: HashSet::new(),
            file_manager,
            piece_selector,
            piece_verifier,
            download_queue_manager,
            event_manager,
            storage_manager,
            event_sender: None,
        })
    }

    /// 获取种子信息
    pub async fn get_torrent_info(&self) -> TorrentInfo {
        self.file_manager.read().await.get_torrent_info().clone()
    }

    /// 更新对等点拥有的分片
    pub fn update_peer_pieces(&mut self, peer_addr: &std::net::SocketAddr, peer_pieces: &HashSet<u32>) {
        self.piece_selector.update_peer_pieces(peer_addr, peer_pieces);
    }

    /// 选择下一个要下载的分片
    pub fn select_next_piece(&mut self, peer_pieces: &HashSet<u32>) -> Option<u32> {
        // 更新分片选择器的状态
        self.piece_selector.update_verified_pieces(self.pieces_verified);

        // 使用分片选择器选择下一个分片
        self.piece_selector.select_next_piece(
            peer_pieces,
            &self.piece_states,
            &self.requested_pieces
        )
    }

    /// 为特定对等点选择下一个要下载的分片
    pub fn select_next_piece_for_peer(
        &mut self,
        peer_addr: &std::net::SocketAddr,
        peer_pieces: &HashSet<u32>
    ) -> Option<u32> {
        self.piece_selector.update_verified_pieces(self.pieces_verified);

        self.piece_selector.select_next_piece_for_peer(
            peer_addr,
            peer_pieces,
            &self.piece_states,
            &self.requested_pieces
        )
    }

    /// 验证分片
    pub fn verify_piece(&mut self, piece_index: u32) -> Result<bool, BitTorrentError> {
        if piece_index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from("Invalid piece index"));
        }

        let piece = &mut self.pieces[piece_index as usize];
        let result = self.piece_verifier.verify_piece(piece);

        if result.is_valid {
            self.piece_states[piece_index as usize] = PieceState::Verified;
            self.pieces_verified += 1;

            // 发布验证成功事件
            let _ = self.event_manager.publish_event(PieceEvent::PieceVerified {
                piece_index,
                verification_time_ms: 0, // 这里应该从验证结果中获取
            });

            Ok(true)
        } else {
            self.piece_states[piece_index as usize] = PieceState::Failed;

            // 发布验证失败事件
            let _ = self.event_manager.publish_event(PieceEvent::PieceVerificationFailed {
                piece_index,
                error: result.error.unwrap_or_else(|| "Unknown error".to_string()),
                retry_count: self.piece_verifier.get_failure_count(piece_index),
            });

            Ok(false)
        }
    }

    /// 存储已验证的分片
    pub async fn store_piece(&mut self, piece_index: u32) -> Result<(), BitTorrentError> {
        if piece_index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from("Invalid piece index"));
        }

        let piece = &self.pieces[piece_index as usize];
        if piece.state != PieceState::Verified {
            return Err(BitTorrentError::from("Cannot store unverified piece"));
        }

        self.storage_manager.store_piece(piece).await?;

        // 发布写入成功事件
        let _ = self.event_manager.publish_event(PieceEvent::PieceWritten {
            piece_index,
            file_offset: piece_index as u64 * piece.size, // 简化计算
            write_time_ms: 0, // 这里应该从存储管理器获取
        });

        Ok(())
    }

    /// 在分片完成时发送事件（向后兼容性）
    pub async fn notify_piece_completed(&mut self, index: u32) {
        // 使用新的事件系统
        let _ = self.event_manager.publish_event(PieceEvent::PieceDownloaded {
            piece_index: index,
            piece_size: self.pieces[index as usize].size,
            total_download_time_ms: 0, // 这里应该跟踪实际下载时间
        });

        // 向后兼容性：使用旧的事件通道
        if let Some(sender) = &self.event_sender {
            let _ = sender.send(PieceEvent::PieceDownloaded {
                piece_index: index,
                piece_size: self.pieces[index as usize].size,
                total_download_time_ms: 0,
            });
        }
    }
}

#[async_trait]
impl PieceManager for BitTorrentPieceManager {
    async fn init(&mut self) -> Result<(),BitTorrentError> {
        if self.initialized {
            return Ok(());
        }

        // 初始化下载队列
        self.download_queue.clear();
        for i in 0..self.pieces.len() {
            if self.pieces[i].state == PieceState::Missing {
                self.download_queue.push_back(i as u32);
            }
        }

        // 设置初始策略为随机首块
        self.piece_selector.set_strategy(PieceSelectionStrategy::RandomFirst);

        self.initialized = true;
        info!("BitTorrent piece manager initialized with {} pieces", self.pieces.len());

        Ok(())
    }

    async fn next_piece(&mut self) -> Result<Option<PieceInfo>, BitTorrentError> {
        // 从下载队列中获取下一个分片
        if let Some(piece_index) = self.download_queue.pop_front() {
            let piece = &self.pieces[piece_index as usize];

            // 如果分片已经下载或验证，跳过
            if piece.state != PieceState::Missing {
                return self.next_piece().await;
            }

            // 创建分片信息
            let piece_info = PieceInfo {
                index: piece_index,
                size: piece.size,
                hash: Some(piece.hash.clone()),
                state: piece.state,
                priority: piece.priority,
                progress: piece.progress,
            };

            // 标记为已请求
            self.requested_pieces.insert(piece_index);

            return Ok(Some(piece_info));
        }

        Ok(None)
    }

    async fn add_piece_data(&mut self, index: u32, data: Vec<u8>) -> Result<bool,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &mut self.pieces[index as usize];

        // 检查分片状态
        if piece.state != PieceState::Missing && piece.state != PieceState::Requested {
            return Ok(false);
        }

        // 检查数据大小
        if data.len() != piece.size as usize {
            return Err(BitTorrentError::from(format!("Piece data size mismatch: expected {}, got {}", piece.size, data.len())));
        }

        // 更新分片状态
        piece.state = PieceState::Downloaded;
        self.pieces_downloaded += 1;
        self.downloaded_size += data.len() as u64;

        // 添加数据到所有块
        let block_size = self.block_size as usize;
        let mut remaining_data = data.as_slice();
        let mut blocks_updated = 0;

        for block in &mut piece.blocks {
            // 计算此块应该包含的数据大小
            let data_size = std::cmp::min(block_size, remaining_data.len());
            if data_size == 0 {
                break;
            }

            // 提取此块的数据
            let block_data = remaining_data[..data_size].to_vec();
            remaining_data = &remaining_data[data_size..];

            // 更新块状态
            block.data = Some(block_data);
            block.state = BlockState::Downloaded;
            blocks_updated += 1;
        }

        // 更新分片状态
        piece.blocks_downloaded += blocks_updated;

        // 更新进度
        self.progress = self.downloaded_size as f64 / self.total_size as f64 * 100.0;

        // 验证分片
        self.verify_piece(index).await?;

        Ok(true)
    }

    async fn verify_piece(&mut self, index: u32) -> Result<bool,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &mut self.pieces[index as usize];

        // 检查分片状态
        if piece.state != PieceState::Downloaded {
            return Ok(false);
        }

        // 验证分片
        let valid = piece.verify()?;

        if valid {
            // 更新分片状态
            piece.state = PieceState::Verified;
            self.pieces_verified += 1;

            // 更新分片状态数组
            self.piece_states[index as usize] = PieceState::Verified;

            // 从已请求集合中移除
            self.requested_pieces.remove(&index);

            // 写入文件
            self.write_piece_to_files(index).await?;

            info!("Piece {} verified and saved, progress: {:.2}%", index, self.progress);
        } else {
            // 验证失败，重置分片状态
            piece.state = PieceState::Failed;
            self.piece_states[index as usize] = PieceState::Failed;

            // 重新加入下载队列
            self.download_queue.push_back(index);

            warn!("Piece {} verification failed, re-queued for download", index);
        }

        Ok(valid)
    }

    async fn piece_state(&self, index: u32) -> Result<PieceState,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        Ok(self.pieces[index as usize].state)
    }

    async fn all_piece_states(&self) -> Result<Vec<PieceState>, BitTorrentError> {
        Ok(self.piece_states.clone())
    }

    async fn progress(&self) -> Result<f64,BitTorrentError> {
        Ok(self.progress)
    }

    async fn downloaded_size(&self) -> Result<u64,BitTorrentError> {
        Ok(self.downloaded_size)
    }

    async fn total_size(&self) -> Result<u64,BitTorrentError> {
        Ok(self.total_size)
    }

    async fn save_data(&self) -> Result<(),BitTorrentError> {
        // 所有分片都是在验证后立即写入文件的，所以这里不需要额外操作
        Ok(())
    }

    async fn is_complete(&self) -> Result<bool,BitTorrentError> {
        Ok(self.pieces_downloaded == self.pieces_total)
    }

    async fn get_piece_info(&self, index: u32) -> Result<Option<PieceInfo>,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &self.pieces[index as usize];

        let piece_info = PieceInfo {
            index,
            size: piece.size,
            hash: Some(piece.hash.clone()),
            state: piece.state,
            priority: piece.priority,
            progress: piece.progress,
        };

        Ok(Some(piece_info))
    }

    async fn get_piece_rarities(&self) -> Result<Vec<usize>,BitTorrentError> {
        let rarities = self.pieces.iter().map(|p| p.rarity).collect();
        Ok(rarities)
    }

    async fn get_requested_pieces(&self) -> Result<HashSet<u32>,BitTorrentError> {
        Ok(self.requested_pieces.clone())
    }

    async fn mark_piece_requested(&mut self, index: u32) -> Result<(), BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }
        self.requested_pieces.insert(index);
        Ok(())
    }

    async fn read_block(&self, index: u32, begin: u32, length: u32) -> Result<Vec<u8>, BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }
        let piece = &self.pieces[index as usize];
        if piece.state != PieceState::Verified {
            return Err(BitTorrentError::from("Cannot read from unverified piece"));
        }
        if begin as u64 + length as u64 > piece.size {
            return Err(BitTorrentError::from(format!("Invalid block range: begin={}, length={}, piece_size={}", begin, length, piece.size)));
        }
        let data = self.file_manager.read_piece_data(index, begin, length).await?;
        if data.len() != length as usize {
            return Err(BitTorrentError::from(format!("Read data size mismatch: expected {}, got {}", length, data.len())));
        }
        Ok(data)
    }
    
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[async_trait]
impl PieceManagerTrait for BitTorrentPieceManager {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    /// 检查分片是否全部完成
    async fn is_complete(&self) -> Result<bool, BitTorrentError> {
        Ok(self.pieces_downloaded == self.pieces_total)
    }
    /// 获取下载进度百分比
    async fn progress(&self) -> Result<f64, BitTorrentError> {
        Ok(self.progress)
    }
    /// 获取已下载数据大小
    async fn downloaded_size(&self) -> Result<u64, BitTorrentError> {
        Ok(self.downloaded_size)
    }
}

#[async_trait]
impl PieceCompleteCallback for BitTorrentPieceManager {
    async fn on_piece_complete(&mut self, piece_index: u32, piece_data: Vec<u8>) -> Result<bool, anyhow::Error> {
        let result = self.add_piece_data(piece_index, piece_data).await;
        if let Ok(true) = result {
            self.notify_piece_completed(piece_index).await;
        }
        result.map_err(|e| anyhow::Error::from(e))
    }
}

#[async_trait]
impl BlockAddedCallback for BitTorrentPieceManager {
    async fn on_block_added(&self, piece_index: u32, progress: f64) -> Result<(), anyhow::Error> {
        debug!("Block added for piece {}, overall progress: {:.2}%", piece_index, progress * 100.0);
        let progress_percent = (progress * 100.0) as u32;
        if progress_percent > 0 && progress_percent % 5 == 0 && progress_percent <= 100 {
            let last_progress_key = format!("last_logged_progress_{}", piece_index);
            let mut last_logged = 0;
            if PIECE_CACHE.get_metadata(&last_progress_key).is_some() {
                if let Some(last_progress) = PIECE_CACHE.get_metadata(&last_progress_key) {
                    if let Ok(last) = last_progress.parse::<u32>() {
                        last_logged = last;
                    }
                }
            }
            if progress_percent > last_logged {
                info!("Download progress: {:.2}%, piece {} updated", progress * 100.0, piece_index);
                PIECE_CACHE.set_metadata(&last_progress_key, &progress_percent.to_string());
            }
        }
        Ok(())
    }
}

// 实现标记性 trait，用于兼容现有代码
impl BlockManagerCallback for BitTorrentPieceManager {}
