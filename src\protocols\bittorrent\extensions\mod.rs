// 扩展协议相关模块统一入口

// 核心扩展模块
pub mod extension_message_handler;
pub mod extension_protocol;
pub mod message;
pub mod metadata;
pub mod pex;
pub mod magnet;

// 重新导出常用类型
pub use extension_message_handler::{ExtensionMessageHand<PERSON>, PeerManagerCallback};
pub use extension_protocol::{ExtensionProtocol, ExtensionHandler};
pub use message::{ExtensionHandshake, ExtensionMessageTrait, ExtensionMessageType};
pub use metadata::MetadataExtension;
pub use pex::{PEXExtension, PEXMessage};

// 导出扩展管理器
pub use crate::protocols::bittorrent::extension_manager;
